/**
 * 错误信息国际化工具
 * 将 better-auth 返回的英文错误信息转换为中文
 */

// 错误信息映射表
const ERROR_MESSAGE_MAP: Record<string, string> = {
  // 基础认证错误
  "Invalid email or password": "邮箱或密码错误",
  "Invalid credentials": "登录凭据无效",
  "User already exists": "用户已存在",
  "Email already exists": "邮箱已被注册",
  "User not found": "用户不存在",
  "Invalid email": "邮箱格式无效",
  "Invalid password": "密码格式无效",
  "Password too short": "密码长度不足",
  "Password too weak": "密码强度不够",
  "Email verification required": "需要验证邮箱",
  "Account not verified": "账户未验证",
  "Login failed": "登录失败",
  "Registration failed": "注册失败",
  "Authentication failed": "认证失败",
  "Signup failed": "注册失败",
  "Sign up failed": "注册失败",
  "Sign in failed": "登录失败",
  "Signin failed": "登录失败",

  // GitHub OAuth 相关错误
  "OAuth authentication failed": "GitHub 登录失败",
  "OAuth callback error": "GitHub 回调错误",
  "GitHub authentication cancelled": "GitHub 登录已取消",
  "GitHub authentication timeout": "GitHub 登录超时",
  "OAuth provider error": "第三方登录服务错误",
  "GitHub account already linked": "该 GitHub 账户已关联其他用户",
  "GitHub account not found": "未找到 GitHub 账户信息",
  "OAuth state mismatch": "登录状态验证失败",
  "OAuth code exchange failed": "登录授权码交换失败",

  // Google OAuth 相关错误
  "Google authentication failed": "Google 登录失败",
  "Google callback error": "Google 回调错误",
  "Google authentication cancelled": "Google 登录已取消",
  "Google authentication timeout": "Google 登录超时",
  "Google account already linked": "该 Google 账户已关联其他用户",
  "Google account not found": "未找到 Google 账户信息",

  // 自动登录相关错误
  "Auto login failed": "自动登录失败",
  "Registration successful but auto login failed": "注册成功，但自动登录失败",
  "Login successful but redirect failed": "登录成功，但页面跳转失败",

  // 会话相关错误
  "Session expired": "会话已过期，请重新登录",
  "Invalid session": "会话无效，请重新登录",
  "Unauthorized": "未授权访问",
  "Access denied": "访问被拒绝",

  // 网络相关错误
  "Network error": "网络错误，请检查网络连接",
  "Server error": "服务器错误，请稍后重试",
  "Request timeout": "请求超时，请重试",

  // 通用错误
  "Something went wrong": "出现了一些问题",
  "Unknown error": "未知错误",
  "Internal server error": "服务器内部错误"
}

/**
 * 将英文错误信息转换为中文
 * @param errorMessage 原始错误信息（通常是英文）
 * @param fallbackMessage 备用错误信息（当没有匹配的翻译时使用）
 * @returns 中文错误信息
 */
export function translateErrorMessage(
  errorMessage: string | undefined | null,
  fallbackMessage: string = "操作失败，请重试"
): string {
  if (!errorMessage) {
    return fallbackMessage
  }

  // 查找精确匹配
  const translatedMessage = ERROR_MESSAGE_MAP[errorMessage]
  if (translatedMessage) {
    return translatedMessage
  }

  // 查找部分匹配（用于处理包含额外信息的错误消息）
  for (const [englishMessage, chineseMessage] of Object.entries(ERROR_MESSAGE_MAP)) {
    if (errorMessage.toLowerCase().includes(englishMessage.toLowerCase())) {
      return chineseMessage
    }
  }

  // 如果没有找到匹配的翻译，返回备用消息
  return fallbackMessage
}

/**
 * 专门用于OAuth登录错误的翻译函数
 * @param errorMessage 登录错误信息
 * @returns 中文错误信息
 */
export function translateOAuthError(errorMessage: string | undefined | null): string {
  return translateErrorMessage(errorMessage, "登录失败，请重试")
}
