import { db } from '@/db'
import { user, account } from '@/db/auth-schema'
import { eq } from 'drizzle-orm'
import type { UserProfile, LoginMethod } from '@/types/profile'

/**
 * 获取用户的完整个人资料信息，包括登录方式
 */
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  try {
    // 获取用户基本信息
    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        image: true,
        emailVerified: true,
        password: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (!userData) {
      return null
    }

    // 获取用户的OAuth账户信息
    const accounts = await db.query.account.findMany({
      where: eq(account.userId, userId),
      columns: {
        providerId: true,
        accountId: true,
      },
    })

    // 构建登录方式列表 - 只包含真正的第三方OAuth账户
    const loginMethods: LoginMethod[] = accounts
      .filter(acc => {
        // 过滤掉非OAuth提供商（如 credential、email 等）
        const oauthProviders = ['github', 'google', 'facebook', 'twitter', 'discord', 'microsoft']
        return oauthProviders.includes(acc.providerId)
      })
      .map(acc => ({
        provider: acc.providerId,
        providerId: acc.providerId,
        accountId: acc.accountId,
      }))

    // 检查是否有密码（邮箱密码登录）
    const hasPassword = !!userData.password

    // 检查是否有credential类型的账户（邮箱密码认证）
    const hasCredentialAccount = accounts.some(acc => acc.providerId === 'credential')

    return {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      image: userData.image,
      emailVerified: userData.emailVerified,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
      loginMethods,
      hasPassword,
      hasCredentialAccount,
    }
  } catch (error) {
    console.error('Error getting user profile:', error)
    return null
  }
}



/**
 * 检查用户是否可以修改密码
 * 有密码或有credential类型账户的用户都可以修改密码
 */
export function canChangePassword(hasPassword: boolean, hasCredentialAccount: boolean): boolean {
  return hasPassword || hasCredentialAccount
}

/**
 * 更新用户昵称
 */
export async function updateUserNickname(userId: string, nickname: string): Promise<{ success: boolean; error?: string }> {
  try {
    // 验证昵称
    if (!nickname || nickname.trim().length === 0) {
      return { success: false, error: '昵称不能为空' }
    }

    const trimmedNickname = nickname.trim()
    if (trimmedNickname.length > 50) {
      return { success: false, error: '昵称长度不能超过50个字符' }
    }

    // 更新数据库
    await db.update(user)
      .set({
        name: trimmedNickname,
        updatedAt: new Date()
      })
      .where(eq(user.id, userId))

    return { success: true }
  } catch (error) {
    console.error('Error updating user nickname:', error)
    return { success: false, error: '更新昵称失败，请稍后重试' }
  }
}
