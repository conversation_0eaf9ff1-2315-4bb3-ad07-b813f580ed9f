import { db } from '@/db'
import { prompts, parameters, categories } from '@/db/schema'
import { user } from '@/db/auth-schema'
import { eq, desc, and, count, like, or } from 'drizzle-orm'
import type { PromptWithParameters } from '@/types'

// 分类选项
export const PROMPT_CATEGORIES = [
  '翻译',
  '编程',
  '办公',
  '商业',
  '教育',
  '设计',
  '未分类'
] as const

export type PromptCategory = typeof PROMPT_CATEGORIES[number]

// 扩展的 Prompt 类型，包含作者信息
export type AdminPromptWithUser = PromptWithParameters & {
  user: {
    id: string
    name: string
    email: string
  }
}

// 分页参数
export interface PaginationParams {
  page: number
  limit: number
}

// 搜索和过滤参数
export interface AdminPromptFilters {
  search?: string
  category?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'name'
  sortOrder?: 'asc' | 'desc'
}

// 分页结果
export interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

/**
 * 获取所有公开的 prompts（管理员视图）
 */
export async function getPublicPromptsForAdmin(
  pagination: PaginationParams,
  filters: AdminPromptFilters = {}
): Promise<PaginatedResult<AdminPromptWithUser>> {
  try {
    const { page, limit } = pagination
    const { search, category, sortBy = 'createdAt', sortOrder = 'desc' } = filters
    
    const offset = (page - 1) * limit
    
    // 构建查询条件
    const conditions = [eq(prompts.isPublic, true)]
    
    if (search) {
      conditions.push(
        or(
          like(prompts.name, `%${search}%`),
          like(prompts.description, `%${search}%`)
        )!
      )
    }
    
    if (category && category !== '全部') {
      // 根据分类名称查找分类 ID
      const categoryRecord = await db.query.categories.findFirst({
        where: eq(categories.name, category),
        columns: { id: true },
      })
      if (categoryRecord) {
        conditions.push(eq(prompts.categoryId, categoryRecord.id))
      }
    }
    
    // 获取总数
    const [totalResult] = await db
      .select({ count: count() })
      .from(prompts)
      .where(and(...conditions))
    
    const total = totalResult.count
    
    // 获取数据
    const result = await db.query.prompts.findMany({
      where: and(...conditions),
      with: {
        parameters: true,
        category: true,
        user: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: sortOrder === 'desc' ? desc(prompts[sortBy]) : prompts[sortBy],
      limit,
      offset,
    })
    
    return {
      data: result as AdminPromptWithUser[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  } catch (error) {
    console.error('获取公开 prompts 失败:', error)
    throw new Error('获取 prompts 列表失败')
  }
}

/**
 * 更新 prompt 的隐私设置
 */
export async function updatePromptPrivacy(
  promptId: number,
  isPublic: boolean
): Promise<void> {
  try {
    await db
      .update(prompts)
      .set({
        isPublic,
        updatedAt: new Date(),
      })
      .where(eq(prompts.id, promptId))
  } catch (error) {
    console.error('更新 prompt 隐私设置失败:', error)
    throw new Error('更新隐私设置失败')
  }
}

/**
 * 更新 prompt 的推荐状态
 */
export async function updatePromptRecommended(
  promptId: number,
  isRecommended: boolean
): Promise<void> {
  try {
    await db
      .update(prompts)
      .set({
        isRecommended,
        updatedAt: new Date(),
      })
      .where(eq(prompts.id, promptId))
  } catch (error) {
    console.error('更新 prompt 推荐状态失败:', error)
    throw new Error('更新推荐状态失败')
  }
}

/**
 * 更新 prompt 的分类
 */
export async function updatePromptCategory(
  promptId: number,
  category: string
): Promise<void> {
  try {
    // 检查分类是否有效
    const isValid = await isCategoryValid(category)
    if (!isValid) {
      throw new Error('无效的分类')
    }

    // 根据分类名称查找分类 ID
    const categoryRecord = await db.query.categories.findFirst({
      where: eq(categories.name, category),
      columns: { id: true },
    })

    if (!categoryRecord) {
      throw new Error('分类不存在')
    }

    await db
      .update(prompts)
      .set({
        categoryId: categoryRecord.id,
        updatedAt: new Date(),
      })
      .where(eq(prompts.id, promptId))
  } catch (error) {
    console.error('更新 prompt 分类失败:', error)
    throw new Error('更新分类失败')
  }
}

/**
 * 批量更新 prompts 的隐私设置
 */
export async function batchUpdatePromptPrivacy(
  promptIds: number[],
  isPublic: boolean
): Promise<void> {
  try {
    if (promptIds.length === 0) return
    
    await db
      .update(prompts)
      .set({
        isPublic,
        updatedAt: new Date(),
      })
      .where(
        or(...promptIds.map(id => eq(prompts.id, id)))!
      )
  } catch (error) {
    console.error('批量更新 prompt 隐私设置失败:', error)
    throw new Error('批量更新隐私设置失败')
  }
}

/**
 * 批量更新 prompts 的分类
 */
export async function batchUpdatePromptCategory(
  promptIds: number[],
  category: string
): Promise<void> {
  try {
    if (promptIds.length === 0) return

    // 检查分类是否有效
    const isValid = await isCategoryValid(category)
    if (!isValid) {
      throw new Error('无效的分类')
    }

    // 根据分类名称查找分类 ID
    const categoryRecord = await db.query.categories.findFirst({
      where: eq(categories.name, category),
      columns: { id: true },
    })

    if (!categoryRecord) {
      throw new Error('分类不存在')
    }

    await db
      .update(prompts)
      .set({
        categoryId: categoryRecord.id,
        updatedAt: new Date(),
      })
      .where(
        or(...promptIds.map(id => eq(prompts.id, id)))!
      )
  } catch (error) {
    console.error('批量更新 prompt 分类失败:', error)
    throw new Error('批量更新分类失败')
  }
}

/**
 * 批量更新 prompts 的推荐状态
 */
export async function batchUpdatePromptRecommended(
  promptIds: number[],
  isRecommended: boolean
): Promise<void> {
  try {
    if (promptIds.length === 0) return

    await db
      .update(prompts)
      .set({
        isRecommended,
        updatedAt: new Date(),
      })
      .where(
        or(...promptIds.map(id => eq(prompts.id, id)))!
      )
  } catch (error) {
    console.error('批量更新 prompt 推荐状态失败:', error)
    throw new Error('批量更新推荐状态失败')
  }
}

/**
 * 获取所有分类列表
 */
export async function getAllCategories(): Promise<string[]> {
  try {
    const allCategories = await db
      .select({
        name: categories.name,
      })
      .from(categories)
      .orderBy(categories.name)

    return allCategories.map(cat => cat.name)
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return [...PROMPT_CATEGORIES] // 回退到默认分类
  }
}

/**
 * 检查分类是否存在
 */
export async function isCategoryValid(categoryName: string): Promise<boolean> {
  try {
    const categoryRecord = await db.query.categories.findFirst({
      where: eq(categories.name, categoryName),
      columns: { id: true },
    })
    return !!categoryRecord
  } catch (error) {
    console.error('检查分类有效性失败:', error)
    return PROMPT_CATEGORIES.includes(categoryName as PromptCategory)
  }
}

/**
 * 获取分类统计信息
 */
export async function getCategoryStats(): Promise<Record<string, number>> {
  try {
    const stats = await db
      .select({
        categoryName: categories.name,
        count: count(prompts.id),
      })
      .from(categories)
      .leftJoin(prompts, and(
        eq(categories.id, prompts.categoryId),
        eq(prompts.isPublic, true)
      ))
      .groupBy(categories.name)

    const result: Record<string, number> = {}
    stats.forEach(stat => {
      result[stat.categoryName] = stat.count
    })

    return result
  } catch (error) {
    console.error('获取分类统计失败:', error)
    return {}
  }
}
