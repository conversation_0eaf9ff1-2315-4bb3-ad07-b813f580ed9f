'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { toast } from 'sonner';
import type { ChatResponse } from '@/types/chat';
import { modelDetails } from '@/app/provider/models';

// 临时聊天记录接口
interface PendingChat {
  id: string;
  title: string;
  isTemporary?: boolean;
}

// Store 状态接口
interface ChatStore {
  // 聊天列表数据
  chats: ChatResponse[];
  isLoading: boolean;
  error: string | null;

  // 临时聊天记录
  pendingChat: PendingChat | null;

  // 操作状态
  isDeleting: boolean;
  isUpdating: boolean;

  // 模型选择状态
  selectedModel: string;
  
  // Actions
  setChats: (chats: ChatResponse[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 聊天操作
  addChat: (chat: ChatResponse) => void;
  updateChat: (chatId: string, updates: Partial<ChatResponse>) => void;
  removeChat: (chatId: string) => void;
  
  // 临时聊天记录操作
  addPendingChat: (id: string, title?: string) => void;
  removePendingChat: (id: string) => void;
  clearPendingChat: () => void;
  replacePendingChatWithReal: (chatId: string, realChat: ChatResponse) => void;

  // 模型选择操作
  setSelectedModel: (modelId: string) => void;
  
  // 异步操作
  fetchChats: () => Promise<void>;
  deleteChat: (chatId: string) => Promise<void>;
  deleteAllChats: () => Promise<void>;
  updateChatTitle: (chatId: string, title: string) => Promise<void>;
  generateSmartTitle: (chatId: string) => Promise<void>;
  
  // 获取合并后的聊天列表（包含临时记录）
  getAllChats: () => (ChatResponse & { isTemporary?: boolean })[];
}

// API 函数
const fetchChatsFromAPI = async (): Promise<ChatResponse[]> => {
  const response = await fetch('/api/chats');
  if (!response.ok) {
    throw new Error('获取聊天列表失败');
  }
  const data = await response.json();
  return data.chats || [];
};

const deleteChatFromAPI = async (chatId: string): Promise<void> => {
  const response = await fetch(`/api/chats/${chatId}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('删除对话失败');
  }
};

const deleteAllChatsFromAPI = async (): Promise<void> => {
  const response = await fetch('/api/chats/delete-all', {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('删除所有对话失败');
  }
};

const updateChatTitleFromAPI = async (chatId: string, title: string): Promise<void> => {
  const response = await fetch(`/api/chats/${chatId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ title }),
  });
  if (!response.ok) {
    throw new Error('更新对话标题失败');
  }
};

const generateSmartTitleFromAPI = async (chatId: string): Promise<{ title: string }> => {
  const response = await fetch(`/api/chats/${chatId}/generate-title`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error('智能标题生成失败');
  }
  return response.json();
};

// 创建 Zustand store
export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => ({
        // 初始状态
        chats: [],
        isLoading: false,
        error: null,
        pendingChat: null,
        isDeleting: false,
        isUpdating: false,
        // 默认选择第一个可用模型
        selectedModel: (() => {
          const availableModels = Object.keys(modelDetails);
          return availableModels.length > 0 ? availableModels[0] : '';
        })(),

        // 基础 setters
      setChats: (chats) => set({ chats }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),

      // 聊天操作
      addChat: (chat) => set((state) => ({
        chats: [chat, ...state.chats]
      })),

      updateChat: (chatId, updates) => set((state) => ({
        chats: state.chats.map(chat => 
          chat.id === chatId ? { ...chat, ...updates } : chat
        )
      })),

      removeChat: (chatId) => set((state) => ({
        chats: state.chats.filter(chat => chat.id !== chatId)
      })),

      // 临时聊天记录操作
      addPendingChat: (id, title = '新的对话') => {
        set({
          pendingChat: {
            id,
            title,
            isTemporary: true,
          }
        });
      },

      removePendingChat: (id) => {
        const { pendingChat } = get();
        if (pendingChat?.id === id) {
          set({ pendingChat: null });
        }
      },

      clearPendingChat: () => {
        set({ pendingChat: null });
      },

      replacePendingChatWithReal: (chatId, realChat) => {
        const { pendingChat, chats } = get();
        // 如果存在对应的临时聊天记录
        if (pendingChat?.id === chatId) {
          // 保持临时聊天记录的标题（"新的聊天"），避免显示用户消息内容
          // 只有当智能标题生成完成后才会更新标题
          const chatWithPreservedTitle = {
            ...realChat,
            title: pendingChat.title // 保持"新的聊天"标题
          };

          // 清除临时记录
          set({ pendingChat: null });

          // 检查真实聊天记录是否已经存在于列表中
          const existingChatIndex = chats.findIndex(chat => chat.id === chatId);

          if (existingChatIndex === -1) {
            // 如果不存在，添加到列表顶部，使用保持的标题
            set((state) => ({
              chats: [chatWithPreservedTitle, ...state.chats]
            }));
          } else {
            // 如果已存在，更新现有记录，使用保持的标题
            set((state) => ({
              chats: state.chats.map((chat, index) =>
                index === existingChatIndex ? chatWithPreservedTitle : chat
              )
            }));
          }
        } else {
          // 如果没有对应的临时记录，直接检查是否需要添加到列表
          const existingChatIndex = chats.findIndex(chat => chat.id === chatId);
          if (existingChatIndex === -1) {
            set((state) => ({
              chats: [realChat, ...state.chats]
            }));
          }
        }
      },

      // 异步操作
      fetchChats: async () => {
        set({ isLoading: true, error: null });
        try {
          const chats = await fetchChatsFromAPI();
          set({ chats, isLoading: false });
        } catch (error) {
          console.error('获取聊天列表失败:', error);
          set({ 
            error: error instanceof Error ? error.message : '获取聊天列表失败',
            isLoading: false 
          });
        }
      },

      deleteChat: async (chatId) => {
        set({ isDeleting: true });
        try {
          await deleteChatFromAPI(chatId);
          get().removeChat(chatId);
          toast.success('对话删除成功');
        } catch (error) {
          console.error('删除对话失败:', error);
          toast.error('删除对话失败');
        } finally {
          set({ isDeleting: false });
        }
      },

      deleteAllChats: async () => {
        set({ isDeleting: true });
        try {
          await deleteAllChatsFromAPI();
          set({ chats: [], pendingChat: null });
          toast.success('所有对话删除成功');
        } catch (error) {
          console.error('删除所有对话失败:', error);
          toast.error('删除所有对话失败');
          throw error;
        } finally {
          set({ isDeleting: false });
        }
      },

      updateChatTitle: async (chatId, title) => {
        set({ isUpdating: true });
        try {
          await updateChatTitleFromAPI(chatId, title);
          get().updateChat(chatId, { title });
          toast.success('对话标题更新成功');
        } catch (error) {
          console.error('更新对话标题失败:', error);
          toast.error('更新对话标题失败');
        } finally {
          set({ isUpdating: false });
        }
      },

      generateSmartTitle: async (chatId) => {
        try {
          const result = await generateSmartTitleFromAPI(chatId);
          const { pendingChat, chats } = get();

          // 优先更新临时聊天记录的标题（如果存在）
          if (pendingChat?.id === chatId) {
            set((state) => ({
              pendingChat: {
                ...state.pendingChat!,
                title: result.title
              }
            }));
          }

          // 同时更新真实聊天记录中的标题（如果存在）
          const existingChatIndex = chats.findIndex(chat => chat.id === chatId);
          if (existingChatIndex !== -1) {
            get().updateChat(chatId, { title: result.title });
          }
        } catch (error) {
          console.error('❌ Zustand: 智能标题生成失败:', error);
          // 不显示错误提示，因为这是后台操作
        }
      },

      // 模型选择操作
      setSelectedModel: (modelId: string) => {
        set({ selectedModel: modelId });
      },

      // 获取合并后的聊天列表
      getAllChats: () => {
        const { chats, pendingChat } = get();
        
        // 如果有临时聊天记录且不在真实聊天记录中，则添加到列表顶部
        if (pendingChat && !chats.some(chat => chat.id === pendingChat.id)) {
          const tempChat: ChatResponse & { isTemporary?: boolean } = {
            id: pendingChat.id,
            title: pendingChat.title,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            messageCount: 0,
            lastMessage: undefined,
            isTemporary: true,
          };
          
          return [tempChat, ...chats];
        }
        
        return chats;
      },
    }),
    {
      name: 'chat-store',
    }
  )
);
