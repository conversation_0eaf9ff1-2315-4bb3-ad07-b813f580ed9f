import { db } from '@/db';
import { chatUsage, type ChatUsage, type NewChatUsage } from '@/db/schema';
import { eq, and, sql } from 'drizzle-orm';

/**
 * 获取聊天限额配置
 */
export function getChatLimit(): number {
  const limit = process.env.CHAT_LIMIT;
  return limit ? parseInt(limit, 10) : 10;
}

/**
 * 获取当前日期字符串 (YYYY-MM-DD)
 */
export function getCurrentDateString(): string {
  const now = new Date();
  return now.toISOString().split('T')[0];
}

/**
 * 获取用户今日的聊天使用记录
 */
export async function getUserDailyUsage(userId: string, date?: string): Promise<ChatUsage | null> {
  const targetDate = date || getCurrentDateString();
  
  const usage = await db.query.chatUsage.findFirst({
    where: and(
      eq(chatUsage.userId, userId),
      eq(chatUsage.date, targetDate)
    ),
  });

  return usage || null;
}

/**
 * 检查用户是否还有剩余的聊天次数
 */
export async function checkChatLimit(userId: string): Promise<{
  canChat: boolean;
  remainingCount: number;
  totalLimit: number;
  usedCount: number;
}> {
  const limit = getChatLimit();
  const usage = await getUserDailyUsage(userId);
  const usedCount = usage?.count || 0;
  const remainingCount = Math.max(0, limit - usedCount);

  return {
    canChat: remainingCount > 0,
    remainingCount,
    totalLimit: limit,
    usedCount,
  };
}

/**
 * 增加用户的聊天使用次数
 */
export async function incrementChatUsage(userId: string): Promise<ChatUsage> {
  const date = getCurrentDateString();
  
  // 尝试更新现有记录
  const existingUsage = await getUserDailyUsage(userId, date);
  
  if (existingUsage) {
    // 更新现有记录
    const [updatedUsage] = await db
      .update(chatUsage)
      .set({
        count: existingUsage.count + 1,
        updatedAt: new Date(),
      })
      .where(eq(chatUsage.id, existingUsage.id))
      .returning();
    
    return updatedUsage;
  } else {
    // 创建新记录
    const newUsage: NewChatUsage = {
      userId,
      date,
      count: 1,
    };
    
    const [createdUsage] = await db
      .insert(chatUsage)
      .values(newUsage)
      .returning();
    
    return createdUsage;
  }
}

/**
 * 获取用户的历史使用统计
 */
export async function getUserUsageHistory(
  userId: string,
  days: number = 30
): Promise<ChatUsage[]> {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);
  
  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = endDate.toISOString().split('T')[0];
  
  const history = await db
    .select()
    .from(chatUsage)
    .where(
      and(
        eq(chatUsage.userId, userId),
        sql`${chatUsage.date} >= ${startDateStr}`,
        sql`${chatUsage.date} <= ${endDateStr}`
      )
    )
    .orderBy(chatUsage.date);
  
  return history;
}

/**
 * 获取所有用户的今日使用统计（管理员功能）
 */
export async function getAllUsersUsageToday(): Promise<Array<{
  userId: string;
  userName: string;
  userEmail: string;
  count: number;
  limit: number;
  remainingCount: number;
}>> {
  const date = getCurrentDateString();
  const limit = getChatLimit();
  
  const result = await db.execute(sql`
    SELECT 
      u.id as user_id,
      u.name as user_name,
      u.email as user_email,
      COALESCE(cu.count, 0) as count
    FROM "user" u
    LEFT JOIN chat_usage cu ON u.id = cu.user_id AND cu.date = ${date}
    ORDER BY COALESCE(cu.count, 0) DESC, u.name ASC
  `);
  
  return result.rows.map((row: any) => ({
    userId: row.user_id,
    userName: row.user_name,
    userEmail: row.user_email,
    count: row.count,
    limit,
    remainingCount: Math.max(0, limit - row.count),
  }));
}

/**
 * 获取使用统计概览（管理员功能）
 */
export async function getUsageOverview(): Promise<{
  totalUsers: number;
  activeUsersToday: number;
  totalChatsToday: number;
  averageChatsPerUser: number;
  usersAtLimit: number;
}> {
  const date = getCurrentDateString();
  
  const result = await db.execute(sql`
    SELECT 
      COUNT(DISTINCT u.id) as total_users,
      COUNT(DISTINCT cu.user_id) as active_users_today,
      COALESCE(SUM(cu.count), 0) as total_chats_today,
      COUNT(CASE WHEN cu.count >= ${getChatLimit()} THEN 1 END) as users_at_limit
    FROM "user" u
    LEFT JOIN chat_usage cu ON u.id = cu.user_id AND cu.date = ${date}
  `);
  
  const row = result.rows[0] as any;
  const totalUsers = parseInt(row.total_users);
  const activeUsersToday = parseInt(row.active_users_today);
  const totalChatsToday = parseInt(row.total_chats_today);
  const usersAtLimit = parseInt(row.users_at_limit);
  
  return {
    totalUsers,
    activeUsersToday,
    totalChatsToday,
    averageChatsPerUser: activeUsersToday > 0 ? totalChatsToday / activeUsersToday : 0,
    usersAtLimit,
  };
}

/**
 * 重置所有用户的每日使用次数（通常用于测试或紧急情况）
 */
export async function resetAllDailyUsage(date?: string): Promise<number> {
  const targetDate = date || getCurrentDateString();
  
  const result = await db
    .delete(chatUsage)
    .where(eq(chatUsage.date, targetDate))
    .returning();
  
  return result.length;
}
