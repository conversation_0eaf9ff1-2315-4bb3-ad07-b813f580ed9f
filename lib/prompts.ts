import { db } from '@/db';
import { prompts, parameters, categories } from '@/db/schema';
import { eq, desc, and, or } from 'drizzle-orm';
import type { PromptFormData, PromptWithParameters } from '@/types';

// 获取指定用户的所有 prompts
export async function getAllPrompts(userId: string): Promise<PromptWithParameters[]> {
  try {
    const result = await db.query.prompts.findMany({
      where: eq(prompts.userId, userId),
      with: {
        parameters: true,
        category: true,
      },
      orderBy: [desc(prompts.updatedAt)],
    });
    return result;
  } catch (error) {
    console.error('Error fetching prompts:', error);
    throw new Error('Failed to fetch prompts');
  }
}

// 根据 ID 获取单个 prompt（验证用户权限）
export async function getPromptById(id: number, userId: string): Promise<PromptWithParameters | null> {
  try {
    const result = await db.query.prompts.findFirst({
      where: and(eq(prompts.id, id), eq(prompts.userId, userId)),
      with: {
        parameters: true,
        category: true,
      },
    });
    return result || null;
  } catch (error) {
    console.error('Error fetching prompt:', error);
    throw new Error('Failed to fetch prompt');
  }
}

// 根据 ID 获取 prompt（支持权限验证：用户自己的 prompt 或公开的 prompt）
export async function getPromptByIdWithPermission(id: number, userId: string): Promise<PromptWithParameters | null> {
  try {
    const result = await db.query.prompts.findFirst({
      where: and(
        eq(prompts.id, id),
        // 允许访问用户自己的 prompt 或公开的 prompt
        or(
          eq(prompts.userId, userId),
          eq(prompts.isPublic, true)
        )
      ),
      with: {
        parameters: true,
        category: true,
      },
    });
    return result || null;
  } catch (error) {
    console.error('Error fetching prompt with permission:', error);
    throw new Error('Failed to fetch prompt');
  }
}

// 获取默认分类 ID
async function getDefaultCategoryId(): Promise<number> {
  const defaultCategory = await db.query.categories.findFirst({
    where: eq(categories.name, '未分类'),
    columns: { id: true },
  });

  if (!defaultCategory) {
    throw new Error('Default category "未分类" not found');
  }

  return defaultCategory.id;
}

// 创建新的 prompt
export async function createPrompt(data: PromptFormData, userId: string): Promise<PromptWithParameters> {
  try {
    // 如果没有指定分类，使用默认分类
    const categoryId = data.categoryId || await getDefaultCategoryId();

    // 创建 prompt
    const [newPrompt] = await db
      .insert(prompts)
      .values({
        name: data.name,
        description: data.description || null,
        content: data.content,
        isPublic: data.isPublic || false,
        categoryId: categoryId,
        userId: userId,
      })
      .returning();

    // 创建 parameters
    if (data.parameters.length > 0) {
      await db.insert(parameters).values(
        data.parameters.map((param) => ({
          promptId: newPrompt.id,
          name: param.name,
          description: param.description || null,
          required: param.required,
          userId: userId,
        }))
      );
    }

    // 返回完整的 prompt 数据
    const result = await db.query.prompts.findFirst({
      where: eq(prompts.id, newPrompt.id),
      with: {
        parameters: true,
        category: true,
      },
    });

    if (!result) {
      throw new Error('Failed to create prompt');
    }

    return result;
  } catch (error) {
    console.error('Error creating prompt:', error);
    throw new Error('Failed to create prompt');
  }
}

// 更新 prompt
export async function updatePrompt(id: number, data: PromptFormData, userId: string): Promise<PromptWithParameters> {
  try {
    // 如果没有指定分类，使用默认分类
    const categoryId = data.categoryId || await getDefaultCategoryId();

    // 更新 prompt（验证用户权限）
    await db
      .update(prompts)
      .set({
        name: data.name,
        description: data.description || null,
        content: data.content,
        isPublic: data.isPublic || false,
        categoryId: categoryId,
        updatedAt: new Date(), // 手动设置更新时间，因为 defaultNow() 只在 INSERT 时生效
      })
      .where(and(eq(prompts.id, id), eq(prompts.userId, userId)));

    // 删除现有的 parameters（验证用户权限）
    await db.delete(parameters).where(and(eq(parameters.promptId, id), eq(parameters.userId, userId)));

    // 创建新的 parameters
    if (data.parameters.length > 0) {
      await db.insert(parameters).values(
        data.parameters.map((param) => ({
          promptId: id,
          name: param.name,
          description: param.description || null,
          required: param.required,
          userId: userId,
        }))
      );
    }

    // 返回更新后的 prompt 数据
    const result = await db.query.prompts.findFirst({
      where: eq(prompts.id, id),
      with: {
        parameters: true,
        category: true,
      },
    });

    if (!result) {
      throw new Error('Failed to update prompt');
    }

    return result;
  } catch (error) {
    console.error('Error updating prompt:', error);
    throw new Error('Failed to update prompt');
  }
}

// 删除 prompt
export async function deletePrompt(id: number, userId: string): Promise<void> {
  try {
    await db.delete(prompts).where(and(eq(prompts.id, id), eq(prompts.userId, userId)));
  } catch (error) {
    console.error('Error deleting prompt:', error);
    throw new Error('Failed to delete prompt');
  }
}

// 检查 prompt 名称是否已存在（在用户范围内）
export async function checkPromptNameExists(name: string, userId: string, excludeId?: number): Promise<boolean> {
  try {
    const result = await db.query.prompts.findFirst({
      where: and(eq(prompts.name, name), eq(prompts.userId, userId)),
      columns: { id: true },
    });

    if (excludeId && result && result.id === excludeId) {
      return false;
    }

    return !!result;
  } catch (error) {
    console.error('Error checking prompt name:', error);
    return false;
  }
}
