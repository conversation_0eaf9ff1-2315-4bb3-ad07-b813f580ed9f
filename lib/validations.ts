import type { PromptFormData, ParameterFormData } from '@/types';

export type ValidationError = {
  field: string;
  message: string;
};

export type ValidationResult = {
  isValid: boolean;
  errors: ValidationError[];
};

// 验证 prompt 表单数据
export function validatePromptForm(data: PromptFormData): ValidationResult {
  const errors: ValidationError[] = [];

  // 验证名称
  if (!data.name || data.name.trim().length === 0) {
    errors.push({ field: 'name', message: '名称不能为空' });
  } else if (data.name.trim().length > 100) {
    errors.push({ field: 'name', message: '名称长度不能超过100个字符' });
  }

  // 验证描述
  if (data.description && data.description.length > 500) {
    errors.push({ field: 'description', message: '描述长度不能超过500个字符' });
  }

  // 验证内容
  if (!data.content || data.content.trim().length === 0) {
    errors.push({ field: 'content', message: '内容不能为空' });
  } else if (data.content.trim().length > 10000) {
    errors.push({ field: 'content', message: '内容长度不能超过10000个字符' });
  }

  // 验证参数
  if (data.parameters && data.parameters.length > 0) {
    const parameterNames = new Set<string>();
    
    data.parameters.forEach((param, index) => {
      const paramErrors = validateParameter(param, index);
      errors.push(...paramErrors);
      
      // 检查参数名称重复
      if (param.name && param.name.trim().length > 0) {
        const trimmedName = param.name.trim();
        if (parameterNames.has(trimmedName)) {
          errors.push({ 
            field: `parameters.${index}.name`, 
            message: '参数名称不能重复' 
          });
        } else {
          parameterNames.add(trimmedName);
        }
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 验证单个参数
export function validateParameter(param: ParameterFormData, index: number): ValidationError[] {
  const errors: ValidationError[] = [];

  // 验证参数名称
  if (!param.name || param.name.trim().length === 0) {
    errors.push({ 
      field: `parameters.${index}.name`, 
      message: '参数名称不能为空' 
    });
  } else if (param.name.trim().length > 50) {
    errors.push({ 
      field: `parameters.${index}.name`, 
      message: '参数名称长度不能超过50个字符' 
    });
  } else if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(param.name.trim())) {
    errors.push({ 
      field: `parameters.${index}.name`, 
      message: '参数名称只能包含字母、数字和下划线，且必须以字母或下划线开头' 
    });
  }

  // 验证参数描述
  if (param.description && param.description.length > 200) {
    errors.push({ 
      field: `parameters.${index}.description`, 
      message: '参数描述长度不能超过200个字符' 
    });
  }

  return errors;
}

// 获取字段错误信息
export function getFieldError(errors: ValidationError[], fieldName: string): string | undefined {
  const error = errors.find(err => err.field === fieldName);
  return error?.message;
}

// 检查字段是否有错误
export function hasFieldError(errors: ValidationError[], fieldName: string): boolean {
  return errors.some(err => err.field === fieldName);
}
