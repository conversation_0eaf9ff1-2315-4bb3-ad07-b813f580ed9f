import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import type { PromptWithParameters } from '@/types';

/**
 * 从 URL 参数加载 prompt 的 Hook
 * 支持 /chat?prompt=[promptID] 路由功能
 */
export function usePromptFromUrl() {
  const searchParams = useSearchParams();
  const promptId = searchParams.get('prompt');
  const [hasShownError, setHasShownError] = useState(false);

  // 查询 prompt 数据
  const {
    data: prompt,
    isLoading,
    error,
    isError
  } = useQuery<PromptWithParameters>({
    queryKey: ['chat-prompt', promptId],
    queryFn: async () => {
      if (!promptId) {
        throw new Error('No prompt ID provided');
      }

      const response = await fetch(`/api/chat/prompt/${promptId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch prompt');
      }

      return response.json();
    },
    enabled: !!promptId, // 只有当 promptId 存在时才执行查询
    retry: false, // 不重试，避免多次显示错误提示
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据是新鲜的
  });

  // 处理错误提示
  useEffect(() => {
    if (isError && error && !hasShownError && promptId) {
      toast.error(error.message || 'Prompt 不存在或已被删除');
      setHasShownError(true);
    }
  }, [isError, error, hasShownError, promptId]);

  // 重置错误状态当 promptId 改变时
  useEffect(() => {
    if (promptId) {
      setHasShownError(false);
    }
  }, [promptId]);

  return {
    promptId,
    prompt,
    isLoading,
    isError,
    error,
    hasPromptParam: !!promptId,
  };
}
