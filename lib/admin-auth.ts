import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import { auth } from '@/lib/auth'
import { db } from '@/db'
import { user } from '@/db/auth-schema'
import { eq } from 'drizzle-orm'

/**
 * 管理员权限验证函数
 * 用于保护管理后台页面，确保只有管理员可以访问
 */
export async function requireAdminAuth(redirectPath?: string) {
  try {
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })
    
    if (!session) {
      const loginUrl = redirectPath 
        ? `/login?redirect=${encodeURIComponent(redirectPath)}`
        : '/login'
      redirect(loginUrl)
    }
    
    // 检查用户是否为管理员
    const userData = await db.query.user.findFirst({
      where: eq(user.id, session.user.id),
      columns: {
        id: true,
        name: true,
        email: true,
        isAdmin: true,
      },
    })
    
    if (!userData || !userData.isAdmin) {
      // 非管理员用户，重定向到首页或显示403错误
      redirect('/?error=access_denied')
    }
    
    return {
      session,
      user: userData
    }
  } catch (error) {
    console.error('管理员权限验证失败:', error)
    const loginUrl = redirectPath 
      ? `/login?redirect=${encodeURIComponent(redirectPath)}`
      : '/login'
    redirect(loginUrl)
  }
}

/**
 * 检查用户是否为管理员（不重定向）
 */
export async function checkAdminPermission(userId: string): Promise<boolean> {
  try {
    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        isAdmin: true,
      },
    })
    
    return userData?.isAdmin || false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

/**
 * 获取用户的管理员状态和基本信息
 */
export async function getUserAdminInfo(userId: string) {
  try {
    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        isAdmin: true,
      },
    })
    
    return userData || null
  } catch (error) {
    console.error('获取用户管理员信息失败:', error)
    return null
  }
}
