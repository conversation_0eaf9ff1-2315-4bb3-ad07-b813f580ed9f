import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { getAllPrompts } from '@/lib/prompts';
import {
  validatePromptDefinition,
  generateParameterSchema,
  replacePromptPlaceholders,
} from './prompts';
import type {
  McpServerConfig,
} from './types';
import {
  McpError,
  MCP_ERROR_CODES,
} from './types';

/**
 * 创建 MCP 服务器实例
 */
export async function createMcpServer(userId: string, method: string): Promise<McpServer> {
  const config: McpServerConfig = {
    name: 'PromptRepo MCP Server',
    version: '1.0.0',
    description: '使用 MCP 进行 Prompt 管理和便捷使用',
    userId,
  };

  const server = new McpServer({
    name: config.name,
    version: config.version,
  },
    {
      capabilities: {
        prompts: {}
      }
    }
  );

  if (!method.startsWith('prompts')) {
    return server;
  }

  try {
    // 从数据库获取用户的所有 prompts
    const userPrompts = await getAllPrompts(userId);
    // 动态注册每个 prompt
    for (const prompt of userPrompts) {
      try {
        // 验证 prompt 定义
        const validation = validatePromptDefinition(prompt);
        if (!validation.isValid) {
          console.warn(`跳过无效的 Prompt "${prompt.name}": ${validation.errors.join(', ')}`);
          continue;
        }

        // 生成参数 schema
        const parameterSchema = generateParameterSchema(prompt);

        // 注册 prompt
        server.prompt(
          prompt.name,
          prompt.description || '',
          parameterSchema.shape,
          (args: Record<string, any>) => {
            try {
              // 替换参数占位符
              const processedContent = replacePromptPlaceholders(prompt.content, args);

              return {
                description: prompt.description || undefined,
                messages: [{
                  role: "user" as const,
                  content: {
                    type: "text" as const,
                    text: processedContent
                  }
                }]
              };
            } catch (error) {
              console.error(`处理 Prompt "${prompt.name}" 时出错:`, error);
              throw new McpError(
                `处理 Prompt "${prompt.name}" 失败: ${error instanceof Error ? error.message : '未知错误'}`,
                MCP_ERROR_CODES.INTERNAL_ERROR
              );
            }
          }
        );
      } catch (error) {
        console.error(`注册 Prompt "${prompt.name}" 失败:`, error);
        // 继续注册其他 prompts，不因单个失败而中断
      }
    }

    console.log(`🎉 MCP 服务器创建完成，共注册了 ${userPrompts.length} 个 Prompts`);
  } catch (error) {
    console.error('获取用户 Prompts 失败:', error);
    throw new McpError(
      '初始化 MCP 服务器失败',
      MCP_ERROR_CODES.INTERNAL_ERROR
    );
  }

  return server;
}
