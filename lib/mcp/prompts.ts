import { z } from 'zod';
import type { PromptWithParameters } from '@/types';
import type {
  Mcp<PERSON>rom<PERSON>,
  McpPromptArgument,
  McpPromptResponse,
  McpResource,
  McpResourceResponse,
  McpToolSchema,
} from './types';
import {
  Mcp<PERSON>rror,
  MCP_ERROR_CODES,
} from './types';

/**
 * 将数据库中的 Prompt 转换为 MCP Prompt 格式
 */
export function convertPromptToMcpPrompt(prompt: PromptWithParameters): McpPrompt {
  const promptArguments: McpPromptArgument[] = prompt.parameters.map(param => ({
    name: param.name,
    description: param.description || undefined,
    required: param.required,
  }));

  return {
    name: prompt.name,
    description: prompt.description || undefined,
    arguments: promptArguments.length > 0 ? promptArguments : undefined,
  };
}

/**
 * 将数据库中的 Prompt 转换为 MCP Resource 格式
 */
export function convertPromptToMcpResource(prompt: PromptWithParameters): McpResource {
  return {
    uri: `prompt://${prompt.id}`,
    name: prompt.name,
    description: prompt.description || undefined,
    mimeType: 'text/plain',
  };
}

/**
 * 生成 Prompt 参数的 Zod Schema
 */
export function generateParameterSchema(prompt: PromptWithParameters): McpToolSchema {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  prompt.parameters.forEach(param => {
    // 目前所有参数都作为字符串处理
    // 未来可以根据参数类型扩展支持更多数据类型
    let field: z.ZodTypeAny = z.string();

    if (param.description) {
      field = field.describe(param.description);
    }

    if (!param.required) {
      field = field.optional();
    }

    schemaFields[param.name] = field;
  });

  return z.object(schemaFields);
}

/**
 * 验证 Prompt 参数
 */
export function validatePromptArguments(
  prompt: PromptWithParameters,
  args: Record<string, any>
): { isValid: boolean; errors?: string[] } {
  try {
    const schema = generateParameterSchema(prompt);
    schema.parse(args);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err =>
        `参数 ${err.path.join('.')}: ${err.message}`
      );
      return { isValid: false, errors };
    }
    return { isValid: false, errors: ['参数验证失败'] };
  }
}

/**
 * 替换 Prompt 内容中的参数占位符
 */
export function replacePromptPlaceholders(
  content: string,
  args: Record<string, any>
): string {
  let result = content;

  // 替换 {{参数名}} 格式的占位符
  Object.entries(args).forEach(([key, value]) => {
    const placeholder = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
    result = result.replace(placeholder, String(value));
  });

  return result;
}

/**
 * 生成 MCP Prompt 响应
 */
export function generateMcpPromptResponse(
  prompt: PromptWithParameters,
  args: Record<string, any>
): McpPromptResponse {
  // 验证参数
  const validation = validatePromptArguments(prompt, args);
  if (!validation.isValid) {
    throw new McpError(
      `参数验证失败: ${validation.errors?.join(', ')}`,
      MCP_ERROR_CODES.INVALID_PARAMS
    );
  }

  // 替换占位符
  const processedContent = replacePromptPlaceholders(prompt.content, args);

  return {
    description: prompt.description || undefined,
    messages: [
      {
        role: 'user',
        content: {
          type: 'text',
          text: processedContent,
        },
      },
    ],
  };
}

/**
 * 生成 MCP Resource 响应
 */
export function generateMcpResourceResponse(
  prompt: PromptWithParameters
): McpResourceResponse {
  return {
    contents: [
      {
        uri: `prompt://${prompt.id}`,
        mimeType: 'text/plain',
        text: prompt.content,
      },
    ],
  };
}

/**
 * 检查 Prompt 内容中使用的参数
 */
export function extractUsedParameters(content: string): string[] {
  const parameterRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g;
  const usedParams: string[] = [];
  let match;

  while ((match = parameterRegex.exec(content)) !== null) {
    const paramName = match[1];
    if (!usedParams.includes(paramName)) {
      usedParams.push(paramName);
    }
  }

  return usedParams;
}

/**
 * 验证 Prompt 定义的完整性
 */
export function validatePromptDefinition(prompt: PromptWithParameters): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // 检查内容中使用的参数是否都有定义
  const usedParams = extractUsedParameters(prompt.content);
  const definedParams = prompt.parameters.map(p => p.name);

  usedParams.forEach(paramName => {
    if (!definedParams.includes(paramName)) {
      errors.push(`内容中使用了未定义的参数: ${paramName}`);
    }
  });

  // 检查定义的参数是否都被使用
  definedParams.forEach(paramName => {
    if (!usedParams.includes(paramName)) {
      warnings.push(`定义的参数未在内容中使用: ${paramName}`);
    }
  });

  // 检查必需参数
  const requiredParams = prompt.parameters.filter(p => p.required);
  if (requiredParams.length === 0 && usedParams.length > 0) {
    warnings.push('所有参数都是可选的，但内容中使用了参数');
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
}

/**
 * 生成 Prompt 的使用示例
 */
export function generatePromptExample(prompt: PromptWithParameters): {
  arguments: Record<string, string>;
  result: string;
} {
  const exampleArgs: Record<string, string> = {};

  prompt.parameters.forEach(param => {
    // 生成示例值
    exampleArgs[param.name] = `示例${param.name}值`;
  });

  const result = replacePromptPlaceholders(prompt.content, exampleArgs);

  return {
    arguments: exampleArgs,
    result,
  };
}

/**
 * 格式化 Prompt 信息用于显示
 */
export function formatPromptInfo(prompt: PromptWithParameters): string {
  const lines: string[] = [];

  lines.push(`名称: ${prompt.name}`);
  if (prompt.description) {
    lines.push(`描述: ${prompt.description}`);
  }

  if (prompt.parameters.length > 0) {
    lines.push('参数:');
    prompt.parameters.forEach(param => {
      const required = param.required ? ' (必需)' : ' (可选)';
      const desc = param.description ? ` - ${param.description}` : '';
      lines.push(`  - ${param.name}${required}${desc}`);
    });
  }

  lines.push('内容:');
  lines.push(prompt.content);

  return lines.join('\n');
}
