import { z } from 'zod';
import type { PromptWithParameters } from '@/types';

/**
 * MCP 协议相关的类型定义
 */

// MCP Prompt 参数 Schema
export interface McpPromptArgument {
  name: string;
  description?: string;
  required?: boolean;
}

// MCP Prompt 定义
export interface McpPrompt {
  name: string;
  description?: string;
  arguments?: McpPromptArgument[];
}

// MCP Prompt 消息
export interface McpPromptMessage {
  role: 'user' | 'assistant' | 'system';
  content: {
    type: 'text' | 'image';
    text?: string;
    data?: string;
    mimeType?: string;
  };
}

// MCP Prompt 响应
export interface McpPromptResponse {
  description?: string;
  messages: McpPromptMessage[];
}

// MCP Resource 定义
export interface McpResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

// MCP Resource 内容
export interface McpResourceContent {
  uri: string;
  mimeType?: string;
  text?: string;
  blob?: string;
}

// MCP Resource 响应
export interface McpResourceResponse {
  contents: McpResourceContent[];
}

// MCP Tool 参数 Schema（使用 zod）
export type McpToolSchema = z.ZodObject<any>;

// MCP Tool 定义
export interface McpTool {
  name: string;
  description?: string;
  inputSchema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

// MCP Tool 调用结果
export interface McpToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
    resource?: string;
  }>;
  isError?: boolean;
}

// 数据库 Prompt 到 MCP Prompt 的转换函数类型
export type PromptToMcpConverter = (prompt: PromptWithParameters) => McpPrompt;

// 数据库 Prompt 到 MCP Resource 的转换函数类型
export type PromptToResourceConverter = (prompt: PromptWithParameters) => McpResource;

// 参数验证 Schema 生成器
export type ParameterSchemaGenerator = (prompt: PromptWithParameters) => McpToolSchema;

// MCP 服务器配置
export interface McpServerConfig {
  name: string;
  version: string;
  description?: string;
  userId: string;
}

// 分页配置
export interface PaginationConfig {
  page?: number;
  limit?: number;
  offset?: number;
}

// 查询选项
export interface QueryOptions extends PaginationConfig {
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 缓存配置
export interface CacheConfig {
  enabled: boolean;
  ttl: number; // Time to live in seconds
  maxSize: number; // Maximum number of cached items
}

// 错误类型
export class McpError extends Error {
  constructor(
    message: string,
    public code: number = -32603,
    public data?: any
  ) {
    super(message);
    this.name = 'McpError';
  }
}

// 常用的 MCP 错误代码
export const MCP_ERROR_CODES = {
  PARSE_ERROR: -32700,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
  INTERNAL_ERROR: -32603,
  SERVER_ERROR: -32000,
  UNAUTHORIZED: -32001,
  FORBIDDEN: -32002,
  NOT_FOUND: -32003,
  TIMEOUT: -32004,
  RATE_LIMITED: -32005,
} as const;

// 性能监控指标
export interface PerformanceMetrics {
  requestCount: number;
  averageResponseTime: number;
  errorCount: number;
  cacheHitRate: number;
  lastRequestTime: Date;
}

// 日志级别
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 日志条目
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  data?: any;
}
