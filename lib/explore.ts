import { db } from '@/db';
import { prompts, parameters, categories } from '@/db/schema';
import { user } from '@/db/auth-schema';
import { eq, desc, and, count, like, or } from 'drizzle-orm';
import type { PromptWithParameters } from '@/types';

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
}

// 搜索和过滤参数
export interface ExploreFilters {
  search?: string;
  categoryId?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'name';
  sortOrder?: 'asc' | 'desc';
}

// 分页结果
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 扩展的 Prompt 类型，包含作者信息
export type ExplorePromptWithUser = PromptWithParameters & {
  user: {
    id: string;
    name: string;
    email: string;
  };
};

/**
 * 获取所有公开的 prompts（发现页面）
 */
export async function getPublicPrompts(
  pagination: PaginationParams,
  filters: ExploreFilters = {}
): Promise<PaginatedResult<ExplorePromptWithUser>> {
  try {
    const { page, limit } = pagination;
    const { search, categoryId, sortBy = 'createdAt', sortOrder = 'desc' } = filters;
    
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const conditions = [eq(prompts.isPublic, true)];
    
    if (search) {
      conditions.push(
        or(
          like(prompts.name, `%${search}%`),
          like(prompts.description, `%${search}%`)
        )!
      );
    }
    
    if (categoryId) {
      conditions.push(eq(prompts.categoryId, categoryId));
    }
    
    // 获取总数
    const [totalResult] = await db
      .select({ count: count() })
      .from(prompts)
      .where(and(...conditions));
    
    const total = totalResult.count;
    
    // 获取数据
    const result = await db.query.prompts.findMany({
      where: and(...conditions),
      with: {
        parameters: true,
        category: true,
        user: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: sortOrder === 'desc' ? desc(prompts[sortBy]) : prompts[sortBy],
      limit,
      offset,
    });

    return {
      data: result as ExplorePromptWithUser[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error('Error fetching public prompts:', error);
    throw new Error('Failed to fetch public prompts');
  }
}

/**
 * 根据 ID 获取单个公开 prompt（不需要用户权限验证）
 */
export async function getPublicPromptById(id: number): Promise<ExplorePromptWithUser | null> {
  try {
    const result = await db.query.prompts.findFirst({
      where: and(eq(prompts.id, id), eq(prompts.isPublic, true)),
      with: {
        parameters: true,
        category: true,
        user: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
    return (result as ExplorePromptWithUser) || null;
  } catch (error) {
    console.error('Error fetching public prompt:', error);
    throw new Error('Failed to fetch public prompt');
  }
}

/**
 * 获取所有分类及其公开 prompt 数量
 */
export async function getCategoriesWithPublicPromptCount(): Promise<Array<{
  id: number;
  name: string;
  promptCount: number;
}>> {
  try {
    const result = await db
      .select({
        id: categories.id,
        name: categories.name,
        promptCount: count(prompts.id),
      })
      .from(categories)
      .leftJoin(prompts, and(eq(categories.id, prompts.categoryId), eq(prompts.isPublic, true)))
      .groupBy(categories.id, categories.name)
      .orderBy(categories.name);

    return result;
  } catch (error) {
    console.error('Error fetching categories with prompt count:', error);
    throw new Error('Failed to fetch categories');
  }
}

/**
 * 根据 ID 获取单个分类信息
 */
export async function getCategoryById(id: number): Promise<{ id: number; name: string } | null> {
  try {
    const result = await db.query.categories.findFirst({
      where: eq(categories.id, id),
      columns: {
        id: true,
        name: true,
      },
    });
    return result || null;
  } catch (error) {
    console.error('Error fetching category by id:', error);
    throw new Error('Failed to fetch category');
  }
}

/**
 * 缓存的分类查询函数，用于在 generateMetadata 和页面组件之间共享数据
 */
const categoryCache = new Map<number, { id: number; name: string } | null>();

export async function getCachedCategoryById(id: number): Promise<{ id: number; name: string } | null> {
  if (categoryCache.has(id)) {
    return categoryCache.get(id)!;
  }

  const category = await getCategoryById(id);
  categoryCache.set(id, category);

  // 清理缓存，避免内存泄漏（保留最近的 100 个条目）
  if (categoryCache.size > 100) {
    const firstKey = categoryCache.keys().next().value;
    if (firstKey !== undefined) {
      categoryCache.delete(firstKey);
    }
  }

  return category;
}

