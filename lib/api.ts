import type { PromptFormData, PromptWithParameters } from '@/types';

// API 基础 URL
const API_BASE = '/api';

// 错误处理函数
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }
  return response.json();
}

// 获取所有 prompts
export async function fetchPrompts(): Promise<PromptWithParameters[]> {
  const response = await fetch(`${API_BASE}/prompts`);
  return handleResponse<PromptWithParameters[]>(response);
}

// 获取单个 prompt
export async function fetchPrompt(id: number): Promise<PromptWithParameters> {
  const response = await fetch(`${API_BASE}/prompts/${id}`);
  return handleResponse<PromptWithParameters>(response);
}

// 创建 prompt
export async function createPromptAPI(data: PromptFormData): Promise<PromptWithParameters> {
  const response = await fetch(`${API_BASE}/prompts`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<PromptWithParameters>(response);
}

// 更新 prompt
export async function updatePromptAPI(id: number, data: PromptFormData): Promise<PromptWithParameters> {
  const response = await fetch(`${API_BASE}/prompts/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return handleResponse<PromptWithParameters>(response);
}

// 删除 prompt
export async function deletePromptAPI(id: number): Promise<void> {
  const response = await fetch(`${API_BASE}/prompts/${id}`, {
    method: 'DELETE',
  });
  await handleResponse<{ success: boolean }>(response);
}

// 导出 prompts
export async function exportPrompts(): Promise<void> {
  const response = await fetch(`${API_BASE}/prompts/export`);
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to export prompts');
  }
  
  // 获取文件名
  const contentDisposition = response.headers.get('Content-Disposition');
  let filename = 'prompts-export.json';
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    if (filenameMatch && filenameMatch[1]) {
      filename = filenameMatch[1];
    }
  }
  
  // 创建 blob 并下载
  const blob = await response.blob();
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// 导入 prompts
export async function importPrompts(file: File): Promise<{ success: boolean; importedCount: number }> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = async (event) => {
      try {
        if (!event.target?.result) {
          throw new Error('Failed to read file');
        }
        
        // 解析 JSON 文件
        const jsonData = JSON.parse(event.target.result as string);
        
        // 发送到 API
        const response = await fetch(`${API_BASE}/prompts/import`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(jsonData),
        });
        
        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to import prompts');
        }
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    // 读取文件内容
    reader.readAsText(file);
  });
}
