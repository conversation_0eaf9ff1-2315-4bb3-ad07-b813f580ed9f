import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import { auth } from '@/lib/auth'

/**
 * 服务器端会话验证函数
 * 用于保护服务器组件，如果用户未登录则重定向到登录页面
 * @param redirectPath 用户原本想要访问的路径，用于登录后重定向
 */
export async function requireAuth(redirectPath?: string) {
  try {
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })
    
    if (!session) {
      const loginUrl = redirectPath 
        ? `/login?redirect=${encodeURIComponent(redirectPath)}`
        : '/login'
      redirect(loginUrl)
    }
    
    return session
  } catch (error) {
    console.error('服务器端认证验证失败:', error)
    const loginUrl = redirectPath 
      ? `/login?redirect=${encodeURIComponent(redirectPath)}`
      : '/login'
    redirect(loginUrl)
  }
}

/**
 * 获取当前用户会话（可选）
 * 不会重定向，如果未登录返回 null
 */
export async function getOptionalSession() {
  try {
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })
    
    return session
  } catch (error) {
    console.error('获取会话失败:', error)
    return null
  }
}
