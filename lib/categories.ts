import { db } from '@/db'
import { categories, prompts } from '@/db/schema'
import { eq, count } from 'drizzle-orm'
import type { Category, CategoryWithStats } from '@/types'

/**
 * 获取所有分类列表
 */
export async function getAllCategories(): Promise<Category[]> {
  try {
    const result = await db.query.categories.findMany({
      orderBy: [categories.name],
    })
    return result
  } catch (error) {
    console.error('Error fetching categories:', error)
    throw new Error('Failed to fetch categories')
  }
}

/**
 * 获取所有分类及其统计信息
 */
export async function getCategoriesWithStats(): Promise<CategoryWithStats[]> {
  try {
    const result = await db
      .select({
        id: categories.id,
        name: categories.name,
        createdAt: categories.createdAt,
        updatedAt: categories.updatedAt,
        promptCount: count(prompts.id),
      })
      .from(categories)
      .leftJoin(prompts, eq(categories.id, prompts.categoryId))
      .groupBy(categories.id, categories.name, categories.createdAt, categories.updatedAt)
      .orderBy(categories.name)

    return result.map(cat => ({
      id: cat.id,
      name: cat.name,
      createdAt: cat.createdAt,
      updatedAt: cat.updatedAt,
      promptCount: cat.promptCount,
    }))
  } catch (error) {
    console.error('Error fetching categories with stats:', error)
    throw new Error('Failed to fetch categories with stats')
  }
}

/**
 * 根据 ID 获取分类
 */
export async function getCategoryById(id: number): Promise<Category | null> {
  try {
    const result = await db.query.categories.findFirst({
      where: eq(categories.id, id),
    })
    return result || null
  } catch (error) {
    console.error('Error fetching category:', error)
    throw new Error('Failed to fetch category')
  }
}

/**
 * 创建新分类
 */
export async function createCategory(name: string): Promise<Category> {
  try {
    const [newCategory] = await db
      .insert(categories)
      .values({
        name: name.trim(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning()

    return newCategory
  } catch (error) {
    console.error('Error creating category:', error)
    throw new Error('Failed to create category')
  }
}

/**
 * 更新分类
 */
export async function updateCategory(id: number, name: string): Promise<Category> {
  try {
    const [updatedCategory] = await db
      .update(categories)
      .set({
        name: name.trim(),
        updatedAt: new Date(),
      })
      .where(eq(categories.id, id))
      .returning()

    if (!updatedCategory) {
      throw new Error('Category not found')
    }

    return updatedCategory
  } catch (error) {
    console.error('Error updating category:', error)
    throw new Error('Failed to update category')
  }
}

/**
 * 删除分类（将相关 prompts 移动到默认分类）
 */
export async function deleteCategory(id: number): Promise<{ deletedCategory: Category; affectedPrompts: number }> {
  try {
    // 获取要删除的分类
    const categoryToDelete = await getCategoryById(id)
    if (!categoryToDelete) {
      throw new Error('Category not found')
    }

    // 不能删除"未分类"
    if (categoryToDelete.name === '未分类') {
      throw new Error('Cannot delete default category')
    }

    // 获取默认分类
    const defaultCategory = await db.query.categories.findFirst({
      where: eq(categories.name, '未分类'),
    })

    if (!defaultCategory) {
      throw new Error('Default category not found')
    }

    // 获取该分类下的 prompt 数量
    const [promptCount] = await db
      .select({ count: count() })
      .from(prompts)
      .where(eq(prompts.categoryId, id))

    // 将该分类下的所有 prompts 移动到默认分类
    if (promptCount.count > 0) {
      await db
        .update(prompts)
        .set({
          categoryId: defaultCategory.id,
          updatedAt: new Date(),
        })
        .where(eq(prompts.categoryId, id))
    }

    // 删除分类
    await db.delete(categories).where(eq(categories.id, id))

    return {
      deletedCategory: categoryToDelete,
      affectedPrompts: promptCount.count,
    }
  } catch (error) {
    console.error('Error deleting category:', error)
    throw new Error('Failed to delete category')
  }
}

/**
 * 检查分类名称是否已存在
 */
export async function checkCategoryNameExists(name: string, excludeId?: number): Promise<boolean> {
  try {
    const result = await db.query.categories.findFirst({
      where: eq(categories.name, name.trim()),
      columns: { id: true },
    })

    if (excludeId && result && result.id === excludeId) {
      return false
    }

    return !!result
  } catch (error) {
    console.error('Error checking category name:', error)
    return false
  }
}

/**
 * 获取默认分类 ID
 */
export async function getDefaultCategoryId(): Promise<number> {
  try {
    const defaultCategory = await db.query.categories.findFirst({
      where: eq(categories.name, '未分类'),
      columns: { id: true },
    })

    if (!defaultCategory) {
      throw new Error('Default category "未分类" not found')
    }

    return defaultCategory.id
  } catch (error) {
    console.error('Error getting default category:', error)
    throw new Error('Failed to get default category')
  }
}
