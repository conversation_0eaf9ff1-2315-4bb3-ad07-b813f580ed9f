import { db } from '@/db';
import { user } from '@/db/auth-schema';
import { eq } from 'drizzle-orm';
import { randomBytes } from 'crypto';

/**
 * MCP API Key 管理工具
 */

/**
 * 生成安全的 API Key
 * 生成固定 32 字符长度的字母数字组合
 */
export function generateApiKey(): string {
  // 字符集：大小写字母 + 数字
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const keyLength = 32;

  // 生成足够的随机字节
  const randomData = randomBytes(keyLength);
  let apiKey = '';

  // 将随机字节转换为字符集中的字符
  for (let i = 0; i < keyLength; i++) {
    apiKey += charset[randomData[i] % charset.length];
  }

  return apiKey;
}

/**
 * 验证 API Key 格式
 */
export function isValidApiKeyFormat(apiKey: string): boolean {
  // API Key 格式: 32位字母数字字符串
  const pattern = /^[A-Za-z0-9]{32}$/;
  return pattern.test(apiKey);
}

/**
 * 根据 API Key 获取用户信息
 */
export async function getUserByApiKey(apiKey: string): Promise<{ id: string; name: string; email: string } | null> {
  try {
    if (!apiKey || !isValidApiKeyFormat(apiKey)) {
      return null;
    }

    const result = await db.query.user.findFirst({
      where: eq(user.mcpApiKey, apiKey),
      columns: {
        id: true,
        name: true,
        email: true,
      },
    });

    return result || null;
  } catch (error) {
    console.error('Error getting user by API key:', error);
    return null;
  }
}

/**
 * 为用户生成新的 API Key
 */
export async function generateUserApiKey(userId: string): Promise<string> {
  try {
    const newApiKey = generateApiKey();

    await db
      .update(user)
      .set({
        mcpApiKey: newApiKey,
        updatedAt: new Date(),
      })
      .where(eq(user.id, userId));

    return newApiKey;
  } catch (error) {
    console.error('Error generating user API key:', error);
    throw new Error('生成 API Key 失败');
  }
}

/**
 * 获取用户的当前 API Key
 */
export async function getUserApiKey(userId: string): Promise<string | null> {
  try {
    const result = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        mcpApiKey: true,
      },
    });

    return result?.mcpApiKey || null;
  } catch (error) {
    console.error('Error getting user API key:', error);
    return null;
  }
}

/**
 * 删除用户的 API Key
 */
export async function revokeUserApiKey(userId: string): Promise<void> {
  try {
    await db
      .update(user)
      .set({
        mcpApiKey: null,
        updatedAt: new Date(),
      })
      .where(eq(user.id, userId));
  } catch (error) {
    console.error('Error revoking user API key:', error);
    throw new Error('撤销 API Key 失败');
  }
}

/**
 * 验证 API Key 并返回用户信息
 */
export async function validateApiKey(apiKey: string): Promise<{
  isValid: boolean;
  user?: { id: string; name: string; email: string };
  error?: string;
}> {
  try {
    if (!apiKey) {
      return {
        isValid: false,
        error: 'API Key 不能为空',
      };
    }

    if (!isValidApiKeyFormat(apiKey)) {
      return {
        isValid: false,
        error: 'API Key 格式无效',
      };
    }

    const userInfo = await getUserByApiKey(apiKey);

    if (!userInfo) {
      return {
        isValid: false,
        error: 'API Key 无效或已过期',
      };
    }

    return {
      isValid: true,
      user: userInfo,
    };
  } catch (error) {
    console.error('Error validating API key:', error);
    return {
      isValid: false,
      error: '验证 API Key 时发生错误',
    };
  }
}

/**
 * 从请求中提取 API Key
 */
export function extractApiKeyFromRequest(request: Request): string | null {
  try {
    const url = new URL(request.url);
    const apiKey = url.searchParams.get('key');
    return apiKey;
  } catch (error) {
    console.error('Error extracting API key from request:', error);
    return null;
  }
}

/**
 * 生成 MCP 连接 URL
 */
export function generateMcpConnectionUrl(baseUrl: string, apiKey: string): string {
  const url = new URL('/api/mcp', baseUrl);
  url.searchParams.set('key', apiKey);
  return url.toString();
}

/**
 * 生成 Claude Desktop 配置示例
 */
export function generateClaudeDesktopConfig(baseUrl: string, apiKey: string): object {
  const mcpUrl = generateMcpConnectionUrl(baseUrl, apiKey);

  return {
    mcpServers: {
      "PromptRepo": {
        command: 'npx',
        args: ['mcp-remote', mcpUrl],
      },
    },
  };
}

/**
 * 安全地显示 API Key（隐藏中间部分）
 */
export function maskApiKey(apiKey: string): string {
  if (!apiKey || apiKey.length !== 32) {
    return '***';
  }

  // 对于 32 字符的 API Key，显示前 6 位和后 6 位，中间用 * 替代
  const start = apiKey.substring(0, 6);
  const end = apiKey.substring(26);
  const middle = '*'.repeat(20);

  return `${start}${middle}${end}`;
}
