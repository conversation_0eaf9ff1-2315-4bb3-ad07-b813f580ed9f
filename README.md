# PromptRepo - 认证系统演示

这是一个基于 Next.js 15 和 Better Auth 构建的现代化认证系统演示应用，展示了完整的邮箱注册和登录功能。

## 功能特性

- ✅ **邮箱注册和登录**：支持邮箱和密码的用户注册和登录
- ✅ **GitHub OAuth 登录**：支持使用 GitHub 账户登录和注册
- ✅ **Prompt 管理系统**：完整的 Prompt 模板管理功能
- ✅ **动态参数配置**：支持为 Prompt 添加可配置参数
- ✅ **表单验证**：客户端和服务端完整的表单验证逻辑
- ✅ **会话管理**：自动会话管理，支持登录状态持久化
- ✅ **路由保护**：受保护的路由，只有已登录用户才能访问
- ✅ **响应式设计**：基于 Tailwind CSS 的现代化 UI 设计
- ✅ **TypeScript 支持**：完整的 TypeScript 类型定义
- ✅ **数据库集成**：使用 Drizzle ORM 和 PostgreSQL

## 技术栈

- **前端框架**：Next.js 15.3.2
- **认证库**：Better Auth 1.2.8
- **数据库 ORM**：Drizzle ORM
- **数据库**：PostgreSQL
- **样式框架**：Tailwind CSS 4
- **UI 组件**：Radix UI
- **类型检查**：TypeScript

## 项目结构

```
├── app/                    # Next.js App Router 页面
│   ├── api/               # API 路由
│   │   ├── auth/          # 认证 API 路由
│   │   └── prompts/       # Prompt 管理 API 路由
│   ├── dashboard/         # 受保护的仪表板页面
│   ├── prompts/           # Prompt 管理页面
│   │   ├── create/        # 创建 Prompt 页面
│   │   ├── edit/[id]/     # 编辑 Prompt 页面
│   │   └── detail/[id]/   # Prompt 详情页面
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── auth/              # 认证相关组件
│   ├── prompts/           # Prompt 管理组件
│   └── ui/                # UI 基础组件
├── hooks/                 # 自定义 React Hooks
├── lib/                   # 工具库和配置
├── db/                    # 数据库配置和模式
├── types/                 # TypeScript 类型定义
├── scripts/               # 工具脚本
├── docs/                  # 文档
└── public/                # 静态资源
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，填入您的配置：

```env
# 数据库连接字符串
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Better Auth 配置
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="http://localhost:3001"

# GitHub OAuth 配置（可选）
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

### 3. 设置数据库

确保您有一个运行中的 PostgreSQL 数据库，然后运行数据库迁移：

```bash
# 生成迁移文件
npx drizzle-kit generate

# 应用迁移
npx drizzle-kit migrate
```

### 4. 配置 GitHub OAuth（可选）

如果要启用 GitHub OAuth 登录功能：

1. 访问 [GitHub Developer Settings](https://github.com/settings/applications)
2. 点击 "New OAuth App" 创建新应用
3. 填写应用信息：
   - Application name: PromptRepo
   - Homepage URL: http://localhost:3001
   - Authorization callback URL: http://localhost:3001/api/auth/callback/github
4. 获取 Client ID 和 Client Secret，添加到 `.env.local` 文件

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3001](http://localhost:3001) 查看应用。

## 使用指南

### 注册新用户

**邮箱注册：**
1. 访问 `/register` 页面
2. 填写姓名、邮箱和密码
3. 点击"注册"按钮
4. 注册成功后会自动登录并跳转到 Prompt 管理页面

**GitHub 注册：**
1. 访问 `/register` 页面
2. 点击"使用 GitHub 注册"按钮
3. 在 GitHub 页面完成授权
4. 自动创建账户并登录，跳转到 Prompt 管理页面

### 用户登录

**邮箱登录：**
1. 访问 `/login` 页面
2. 输入邮箱和密码
3. 点击"登录"按钮
4. 登录成功后跳转到 Prompt 管理页面

**GitHub 登录：**
1. 访问 `/login` 页面
2. 点击"使用 GitHub 登录"按钮
3. 在 GitHub 页面完成授权
4. 登录成功后跳转到 Prompt 管理页面

### 访问受保护的页面

- `/dashboard` - 用户仪表板（需要登录）
- `/prompts` - Prompt 管理页面（需要登录）
- 未登录用户访问受保护页面会自动跳转到登录页面

### Prompt 管理

#### 查看 Prompt 列表
访问 `/prompts` 页面查看所有已创建的 Prompt 模板。

#### 创建新 Prompt
1. 在 Prompt 列表页面点击"创建 Prompt"按钮
2. 填写 Prompt 的基本信息（名称、描述、内容）
3. 添加参数配置（可选）
4. 点击"创建"保存

#### 编辑 Prompt
1. 在列表页面点击编辑按钮
2. 修改需要更新的字段
3. 点击"保存"更新

#### 查看 Prompt 详情
点击查看按钮可以查看 Prompt 的完整信息，包括参数列表。

#### 添加演示数据
运行以下命令添加一些演示 Prompt：

```bash
npx tsx scripts/seed-prompts.ts
```

### 登出

点击导航栏中的"登出"按钮即可安全登出。

## API 路由

### 认证 API
- `POST /api/auth/sign-up` - 用户注册
- `POST /api/auth/sign-in` - 用户登录
- `POST /api/auth/sign-out` - 用户登出
- `GET /api/auth/session` - 获取当前会话

### Prompt 管理 API
- `GET /api/prompts` - 获取所有 Prompts
- `POST /api/prompts` - 创建新 Prompt
- `GET /api/prompts/[id]` - 获取单个 Prompt
- `PUT /api/prompts/[id]` - 更新 Prompt
- `DELETE /api/prompts/[id]` - 删除 Prompt

## 组件说明

### 认证组件

- `RegisterForm` - 注册表单组件
- `LoginForm` - 登录表单组件
- `ProtectedRoute` - 路由保护组件

### Prompt 管理组件

- `PromptList` - Prompt 列表组件
- `PromptForm` - Prompt 表单组件（创建/编辑）
- `PromptDetail` - Prompt 详情组件
- `ParameterForm` - 参数配置组件

### UI 组件

- `Button` - 按钮组件
- `Input` - 输入框组件
- `Label` - 标签组件
- `Card` - 卡片组件
- `Dialog` - 对话框组件
- `Textarea` - 文本域组件

### Hooks

- `useSession` - 会话管理 Hook

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 数据库相关
npx drizzle-kit generate    # 生成迁移文件
npx drizzle-kit migrate     # 应用迁移

# 工具脚本
npx tsx scripts/seed-prompts.ts    # 添加演示数据
npx tsx scripts/test-prompts.ts    # 测试 Prompt 功能
```

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 其他平台

确保在部署平台中配置以下环境变量：

- `DATABASE_URL`
- `BETTER_AUTH_SECRET`
- `BETTER_AUTH_URL`
- `GITHUB_CLIENT_ID`（如果启用 GitHub OAuth）
- `GITHUB_CLIENT_SECRET`（如果启用 GitHub OAuth）

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
