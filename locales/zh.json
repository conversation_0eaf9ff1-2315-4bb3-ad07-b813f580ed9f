{"navigation": {"home": "首页", "explore": "发现", "promptManagement": "Prompt 管理", "mcpUsage": "MCP 指引", "chat": "AI 对话", "feedback": "建议反馈", "welcome": "欢迎, {name}", "logout": "登出", "login": "登录", "signup": "注册"}, "auth": {"login": {"title": "登录", "email": "邮箱", "password": "密码", "emailPlaceholder": "请输入邮箱", "passwordPlaceholder": "请输入密码", "loginButton": "登录", "loginLoading": "登录中...", "githubLogin": "使用 GitHub 登录", "googleLogin": "使用 Google 登录", "connectingGithub": "连接 GitHub...", "connectingGoogle": "连接 Google...", "or": "或", "noAccount": "还没有账户？", "signupNow": "立即注册", "agreeToTerms": "登录代表同意", "termsOfService": "服务条款", "and": "和", "privacyPolicy": "隐私政策"}, "signup": {"title": "注册账户", "name": "昵称", "email": "邮箱", "password": "密码", "confirmPassword": "确认密码", "namePlaceholder": "请输入昵称", "emailPlaceholder": "请输入邮箱", "passwordPlaceholder": "请输入密码（至少8位）", "confirmPasswordPlaceholder": "请再次输入密码", "signupButton": "注册", "signupLoading": "注册中...", "githubSignup": "使用 GitHub 注册", "googleSignup": "使用 Google 注册", "hasAccount": "已有账户？", "loginNow": "立即登录", "agreeToTerms": "注册代表同意"}, "oauth": {"processing": "处理登录...", "success": "登录成功", "failed": "登录失败", "processingDescription": "正在验证您的登录信息，请稍候...", "successDescription": "欢迎回来！即将跳转到您的目标页面。", "failedDescription": "很抱歉，登录过程中遇到了问题。", "verifying": "验证中...", "successStatus": "成功", "failedStatus": "失败", "autoRedirect": "即将自动跳转到登录页面..."}, "errors": {"githubLoginFailed": "GitHub 登录失败，请重试", "googleLoginFailed": "Google 登录失败，请重试", "passwordMismatch": "密码确认不匹配", "passwordTooShort": "密码长度至少为8位", "signupSuccess": "注册成功！正在跳转...", "signupFailed": "注册失败，请重试", "loginSuccess": "登录成功！正在跳转...", "loginFailed": "登录失败，请检查邮箱和密码"}}, "prompts": {"title": "Prompt 管理", "create": "创建 Prompt", "edit": "编辑 Prompt", "update": "Prompt 更新", "save": "保存", "creating": "创建中...", "saving": "保存中...", "noPrompts": "暂无 Prompt", "noPromptsDescription": "开始创建您的第一个 Prompt", "export": "导出", "exportSuccess": "导出成功", "import": "导入", "importSuccess": "成功导入 {count} 条 Prompts", "invalidFileType": "文件类型无效，请选择 JSON 文件。", "table": {"name": "名称", "description": "描述", "parameters": "参数数量", "updatedAt": "更新时间", "actions": "操作", "noDescription": "无描述", "isPublic": "公开状态"}, "actions": {"edit": "编辑", "delete": "删除"}, "delete": {"confirmTitle": "确认删除", "confirmDescription": "您确定要删除 Prompt \"{name}\" 吗？此操作无法撤销。", "cancel": "取消", "delete": "删除", "deleting": "删除中...", "success": "删除成功", "failed": "删除 Prompt 失败"}, "form": {"basicInfo": "基本信息", "content": "Prompt 内容", "contentTip": "通过 '{{'xxx'}}' 使用底部设置的参数，调用时会动态替换为调用时填写的参数值", "namePlaceholder": "请输入 Prompt 名称", "descriptionPlaceholder": "请输入 Prompt 描述（可选）", "contentPlaceholder": "请输入 Prompt 内容", "isPublic": "公开", "isPublicDescription": "公开的 Prompt 可以被其他用户查看和使用", "parameters": "参数配置", "addParameter": "添加参数", "noParameters": "暂无参数", "noParametersDescription": "点击\"添加参数\"按钮开始添加", "parameterNumber": "参数 {number}", "parameterName": "参数名称", "parameterNamePlaceholder": "请输入参数名称", "required": "必填参数", "parameterDescription": "参数描述", "parameterDescriptionPlaceholder": "请输入参数描述（可选）", "parameterNameRule": "提示：参数名称只能包含字母、数字和下划线，且必须以字母或下划线开头"}, "status": {"public": "公开", "private": "私有"}}, "explore": {"title": "发现 Prompt", "subtitle": "探索社区分享的优质 Prompt 模板", "allCategories": "全部", "noPrompts": "暂无公开的 Prompt", "noPromptsDescription": "等待更多用户分享他们的 Prompt", "promptCount": "{count} 个 Prompt", "totalResults": "共 {total} 个 Prompt，第 {page} 页，共 {totalPages} 页", "author": "作者", "createdAt": "创建时间", "parameters": "{count} 个参数", "backToExplore": "返回发现页面", "copyContent": "复制内容", "contentCopied": "内容已复制到剪贴板", "copyFailed": "复制失败", "loadFailed": "加载失败", "loadFailedDescription": "无法加载页面，请稍后重试", "promptNotFound": "Prompt 不存在或未公开", "category": "分类", "publicPrompt": "公开 Prompt"}, "validation": {"nameRequired": "名称不能为空", "nameTooLong": "名称长度不能超过100个字符", "descriptionTooLong": "描述长度不能超过500个字符", "contentRequired": "内容不能为空", "contentTooLong": "内容长度不能超过10000个字符", "parameterNameDuplicate": "参数名称不能重复", "parameterNameRequired": "参数名称不能为空", "parameterNameTooLong": "参数名称长度不能超过50个字符", "parameterNameInvalid": "参数名称只能包含字母、数字和下划线，且必须以字母或下划线开头", "parameterDescriptionTooLong": "参数描述长度不能超过200个字符"}, "mcp": {"title": "MCP 服务器管理", "loading": "加载中...", "connectionInfo": "连接信息", "connectionDescription": "使用以下信息连接到您的 MCP 服务器，使用 Streamable HTTP 方式连接", "mcpServer": "MCP 服务器（Streamable HTTP）", "copy": "复制", "regenerate": "重新生成", "regenerating": "重新生成", "autoGenerating": "正在为您自动生成 API Key", "autoGeneratingDescription": "正在为您自动生成 API Key，请稍候...", "generating": "生成中...", "usageGuide": "使用指引", "usageGuideDescription": "选择您使用的客户端，查看相应的配置和使用说明", "claude": {"title": "<PERSON> 配置", "step1": "1. 复制配置信息", "step1Description": "复制以下配置信息", "copyConfig": "复制配置", "step2": "2. 配置 <PERSON>", "step2Description": "进入设置页（MAC 下快捷键为 ⌘,）切换到 Developer 标签，点击 Edit Config，将以上复制的内容粘贴到配置文件中。", "step3": "3. 使用 Prompts", "step3Description": "在对话窗口，点击左下角 + ，选择 PromptRepo 即可使用您的 Prompt 模板库了。"}, "deepchat": {"title": "DeepChat 配置", "step1": "1. 复制连接地址", "step1Description": "复制顶部 Streamable HTTP 连接地址", "step2": "2. 配置 MCP 服务器", "step2Description": "在 DeepChat 设置中添加 MCP 服务器配置。", "step3": "3. 开始使用", "step3Description": "配置完成后，在 DeepChat 对话窗口，输入 @ 即可快捷调用 Prompt"}, "cherrystudio": {"title": "CherryStudio 配置", "step1": "1. 复制连接地址", "step1Description": "复制顶部 Streamable HTTP 连接地址。", "step2": "2. 配置 MCP 服务器", "step2Description": "在 CherryStudio 设置中添加 MCP 服务器配置。", "step3": "3. 开始使用", "step3Description": "配置完成后，在 CherryStudio 对话窗口，输入快捷键 / 选择 MCP 提示即可快捷调用 Prompt"}, "messages": {"confirmRegenerate": "确定生成一个新的 API Key 吗？生成后旧地址将会失效。", "generateSuccess": "API Key 生成成功", "generateFailed": "生成 API Key 失败", "autoGenerateFailed": "自动生成 API Key 失败", "copySuccess": "{label} 已复制到剪贴板", "copyFailed": "复制失败", "fetchFailed": "获取 API Key 信息失败"}}, "common": {"loading": "加载中...", "dataRefreshed": "数据已刷新", "operationFailed": "操作失败，请重试", "operationSuccess": "成功", "unknownError": "未知错误", "back": "返回", "cancel": "取消", "exporting": "导出中...", "importing": "导入中...", "errorOccurred": "发生错误"}, "errors": {"sessionFailed": "获取会话失败", "logoutFailed": "登出失败", "githubSignupFailed": "GitHub 注册失败", "googleSignupFailed": "Google 注册失败", "emailSignupFailed": "邮箱注册失败", "githubLoginFailed": "GitHub 登录失败", "googleLoginFailed": "Google 登录失败", "emailLoginFailed": "邮箱登录失败", "oauthCallbackFailed": "OAuth 回调处理失败", "loginProcessError": "登录过程中发生错误，请重试", "unknownError": "未知错误"}, "ui": {"back": "返回", "edit": "编辑", "delete": "删除", "cancel": "取消", "save": "保存", "create": "创建", "saving": "保存中...", "creating": "创建中...", "deleting": "删除中...", "copied": "已复制", "copy": "复制", "required": "必填", "optional": "可选", "noData": "暂无数据", "loadingData": "加载数据中...", "refreshData": "刷新数据", "checkFormErrors": "请检查表单中的错误", "confirmAction": "确认操作", "cannotUndo": "此操作无法撤销", "processing": "处理中...", "success": "成功", "failed": "失败", "autoRedirect": "即将自动跳转...", "createdAt": "创建时间", "updatedAt": "更新时间", "basicInfo": "基本信息", "content": "内容", "parameterList": "参数列表", "parameterCount": "参数数量", "noParameters": "暂无参数", "parameterUsageTip": "通过 '{{'xxx'}}' 使用底部设置的参数，调用时会动态替换为调用时填写的参数值", "name": "名称", "description": "描述", "promptContent": "Prompt 内容", "namePlaceholder": "请输入 Prompt 名称", "descriptionPlaceholder": "请输入 Prompt 描述（可选）", "contentPlaceholder": "请输入 Prompt 内容"}, "chat": {"title": "AI 对话", "newChat": "新建对话", "startNewChat": "开始新对话", "sendMessage": "发送消息", "inputPlaceholder": "输入消息...", "continueChat": "继续对话...", "startNewChatPlaceholder": "开始新对话...", "noChats": "暂无对话历史", "noMessages": "发送消息开始与 AI 助手对话", "loading": "加载中...", "loadingChats": "加载对话列表中...", "loadingMessages": "加载对话中...", "sending": "发送中...", "streaming": "AI 正在回复...", "stopStreaming": "停止生成", "copyMessage": "复制消息", "copied": "已复制到剪贴板", "copyFailed": "复制失败", "today": "今天", "yesterday": "昨天", "daysAgo": "{days}天前", "messageCount": "{count} 条消息", "inputTip": "按 Enter 发送消息，Shift + Enter 换行", "actions": {"rename": "重命名", "delete": "删除", "edit": "编辑"}, "delete": {"confirmTitle": "确认删除对话", "confirmDescription": "您确定要删除对话 \"{title}\" 吗？此操作无法撤销。", "cancel": "取消", "delete": "删除", "deleting": "删除中...", "success": "对话删除成功", "failed": "删除对话失败"}, "rename": {"title": "重命名对话", "description": "为对话设置一个新的标题", "placeholder": "输入新标题", "cancel": "取消", "save": "保存", "saving": "保存中...", "success": "对话标题更新成功", "failed": "更新对话标题失败"}, "errors": {"loadChatsFailed": "加载对话列表失败", "loadMessagesFailed": "加载对话消息失败", "sendMessageFailed": "发送消息失败", "chatNotFound": "对话不存在", "unauthorized": "未授权访问", "serverError": "服务器内部错误", "invalidChatId": "无效的对话 ID", "emptyMessage": "消息不能为空", "emptyTitle": "对话标题不能为空", "openrouterConfigError": "OpenRouter API 配置错误"}}}