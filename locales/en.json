{"navigation": {"home": "Home", "explore": "Explore", "promptManagement": "Prompt", "mcpUsage": "MCP Guide", "feedback": "<PERSON><PERSON><PERSON>", "welcome": "Welcome, {name}", "logout": "Logout", "login": "<PERSON><PERSON>", "signup": "Sign Up"}, "auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "loginButton": "<PERSON><PERSON>", "loginLoading": "Logging in...", "githubLogin": "Login with GitHub", "googleLogin": "Login with Google", "connectingGithub": "Connecting to GitHub...", "connectingGoogle": "Connecting to Google...", "or": "or", "noAccount": "Don't have an account?", "signupNow": "Sign up now", "agreeToTerms": "By logging in, you agree to our", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy"}, "signup": {"title": "Create Account", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "namePlaceholder": "Enter your name", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter password (at least 8 characters)", "confirmPasswordPlaceholder": "Enter password again", "signupButton": "Sign Up", "signupLoading": "Signing up...", "githubSignup": "Sign up with GitHub", "googleSignup": "Sign up with Google", "hasAccount": "Already have an account?", "loginNow": "Login now", "agreeToTerms": "By signing up, you agree to our"}, "oauth": {"processing": "Processing login...", "success": "Login successful", "failed": "<PERSON><PERSON> failed", "processingDescription": "Verifying your login information, please wait...", "successDescription": "Welcome back! Redirecting to your destination page.", "failedDescription": "Sorry, there was a problem during the login process.", "verifying": "Verifying...", "successStatus": "Success", "failedStatus": "Failed", "autoRedirect": "Redirecting to login page automatically..."}, "errors": {"githubLoginFailed": "GitHub login failed, please try again", "googleLoginFailed": "Google login failed, please try again", "passwordMismatch": "Password confirmation does not match", "passwordTooShort": "Password must be at least 8 characters", "signupSuccess": "Sign up successful! Redirecting...", "signupFailed": "Sign up failed, please try again", "loginSuccess": "Login successful! Redirecting...", "loginFailed": "<PERSON><PERSON> failed, please check your email and password"}}, "prompts": {"title": "Prompt", "create": "Create", "edit": "Edit Prompt", "update": "Update Prompt", "save": "Save", "creating": "Creating...", "saving": "Saving...", "noPrompts": "No Prompts", "noPromptsDescription": "Start creating your first Prompt", "export": "Export", "exportSuccess": "Prompts exported successfully", "import": "Import", "importSuccess": "Successfully imported {count} prompts", "invalidFileType": "Invalid file type. Please select a JSON file.", "table": {"name": "Name", "description": "Description", "parameters": "Parameters", "updatedAt": "Updated", "actions": "Actions", "noDescription": "No description", "isPublic": "Public Status"}, "actions": {"edit": "Edit", "delete": "Delete"}, "delete": {"confirmTitle": "Confirm Delete", "confirmDescription": "Are you sure you want to delete Prompt \"{name}\"? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "success": "Prompt deleted successfully", "failed": "Failed to delete Prompt"}, "form": {"basicInfo": "Basic Information", "content": "Prompt Content", "contentTip": "Use '{{'xxx'}}' to reference parameters configured below, which will be dynamically replaced with values provided during invocation", "namePlaceholder": "Enter Prompt name", "descriptionPlaceholder": "Enter Prompt description (optional)", "contentPlaceholder": "Enter Prompt content", "isPublic": "Public", "isPublicDescription": "Public prompts can be viewed and used by other users", "parameters": "Parameter Configuration", "addParameter": "Add Parameter", "noParameters": "No parameters", "noParametersDescription": "Click \"Add Parameter\" button to start adding", "parameterNumber": "Parameter {number}", "parameterName": "Parameter Name", "parameterNamePlaceholder": "Enter parameter name", "required": "Required", "parameterDescription": "Parameter Description", "parameterDescriptionPlaceholder": "Enter parameter description (optional)", "parameterNameRule": "Tip: Parameter names can only contain letters, numbers, and underscores, and must start with a letter or underscore"}, "status": {"public": "Public", "private": "Private"}}, "explore": {"title": "发现 Prompt", "subtitle": "探索社区分享的优质 Prompt 模板", "allCategories": "全部", "noPrompts": "暂无公开的 Prompt", "noPromptsDescription": "等待更多用户分享他们的 Prompt", "promptCount": "{count} 个 Prompt", "totalResults": "共 {total} 个 Prompt，第 {page} 页，共 {totalPages} 页", "author": "作者", "createdAt": "创建时间", "parameters": "{count} 个参数", "backToExplore": "返回发现页面", "copyContent": "复制内容", "contentCopied": "内容已复制到剪贴板", "copyFailed": "复制失败", "loadFailed": "加载失败", "loadFailedDescription": "无法加载页面，请稍后重试", "promptNotFound": "Prompt 不存在或未公开", "category": "分类", "publicPrompt": "公开 Prompt"}, "validation": {"nameRequired": "Name is required", "nameTooLong": "Name cannot exceed 100 characters", "descriptionTooLong": "Description cannot exceed 500 characters", "contentRequired": "Content is required", "contentTooLong": "Content cannot exceed 10000 characters", "parameterNameDuplicate": "Parameter names cannot be duplicated", "parameterNameRequired": "Parameter name is required", "parameterNameTooLong": "Parameter name cannot exceed 50 characters", "parameterNameInvalid": "Parameter names can only contain letters, numbers, and underscores, and must start with a letter or underscore", "parameterDescriptionTooLong": "Parameter description cannot exceed 200 characters"}, "mcp": {"title": "MCP Server Management", "loading": "Loading...", "connectionInfo": "Connection Info", "connectionDescription": "Use the following information to connect to your MCP server using Streamable HTTP", "mcpServer": "MCP Server (Streamable HTTP)", "copy": "Copy", "regenerate": "Regenerate", "regenerating": "Regenerating", "autoGenerating": "Auto-generating API Key for you", "autoGeneratingDescription": "Auto-generating API Key for you, please wait...", "generating": "Generating...", "usageGuide": "Usage Guide", "usageGuideDescription": "Select your client to view the corresponding configuration and usage instructions", "claude": {"title": "Claude Desktop Configuration", "step1": "1. Copy Configuration", "step1Description": "Copy the following configuration information", "copyConfig": "<PERSON><PERSON> Config", "step2": "2. Configure <PERSON>", "step2Description": "Go to Settings (⌘, on Mac), switch to Developer tab, click Edit Config, and paste the copied content into the configuration file.", "step3": "3. Use Prompts", "step3Description": "In the chat window, click the + button in the bottom left corner, select PromptRepo to use your Prompt template library."}, "deepchat": {"title": "DeepChat Configuration", "step1": "1. Copy Connection URL", "step1Description": "Copy the Streamable HTTP connection URL above", "step2": "2. Configure MCP Server", "step2Description": "Add MCP server configuration in DeepChat settings.", "step3": "3. Start Using", "step3Description": "After configuration, type @ in DeepChat chat window to quickly call Prompts"}, "cherrystudio": {"title": "CherryStudio Configuration", "step1": "1. Copy Connection URL", "step1Description": "Copy the Streamable HTTP connection URL above.", "step2": "2. Configure MCP Server", "step2Description": "Add MCP server configuration in CherryStudio settings.", "step3": "3. Start Using", "step3Description": "After configuration, press / in CherryStudio chat window and select MCP prompts to quickly call Prompts"}, "messages": {"confirmRegenerate": "Are you sure you want to generate a new API Key? The old URL will become invalid after generation.", "generateSuccess": "API Key generated successfully", "generateFailed": "Failed to generate API Key", "autoGenerateFailed": "Failed to auto-generate API Key", "copySuccess": "{label} copied to clipboard", "copyFailed": "Co<PERSON> failed", "fetchFailed": "Failed to fetch API Key information"}}, "common": {"loading": "Loading...", "dataRefreshed": "Data refreshed", "operationFailed": "Operation failed, please try again", "operationSuccess": "Success", "unknownError": "unknown Error", "back": "Back", "cancel": "Cancel", "exporting": "Exporting...", "importing": "Importing...", "errorOccurred": "An error occurred"}, "errors": {"sessionFailed": "Failed to get session", "logoutFailed": "Logout failed", "githubSignupFailed": "GitHub signup failed", "googleSignupFailed": "Google signup failed", "emailSignupFailed": "Email signup failed", "githubLoginFailed": "GitHub login failed", "googleLoginFailed": "Google login failed", "emailLoginFailed": "Email login failed", "oauthCallbackFailed": "OAuth callback processing failed", "loginProcessError": "An error occurred during the login process, please try again", "unknownError": "Unknown error"}, "ui": {"back": "Back", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save", "create": "Create", "saving": "Saving...", "creating": "Creating...", "deleting": "Deleting...", "copied": "<PERSON>pied", "copy": "Copy", "required": "Required", "optional": "Optional", "noData": "No data", "loadingData": "Loading data...", "refreshData": "Refresh data", "checkFormErrors": "Please check form errors", "confirmAction": "Confirm action", "cannotUndo": "This action cannot be undone", "processing": "Processing...", "success": "Success", "failed": "Failed", "autoRedirect": "Redirecting automatically...", "createdAt": "Created", "updatedAt": "Updated", "basicInfo": "Basic Information", "content": "Content", "parameterList": "Parameter List", "parameterCount": "Parameter Count", "noParameters": "No parameters", "parameterUsageTip": "Use '{{'xxx'}}' to reference parameters configured below, which will be dynamically replaced with values provided during invocation", "name": "Name", "description": "Description", "promptContent": "Prompt Content", "namePlaceholder": "Enter Prompt name", "descriptionPlaceholder": "Enter Prompt description (optional)", "contentPlaceholder": "Enter Prompt content"}}