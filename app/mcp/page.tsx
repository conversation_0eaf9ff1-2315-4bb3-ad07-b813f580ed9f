'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import Image from "next/image"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { Copy, RefreshCw } from 'lucide-react';
import { CardLoading, ButtonLoading } from '@/components/ui/loading';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface McpAuthData {
  hasApiKey: boolean;
  apiKey: string | null;
  maskedApiKey: string | null;
  connectionUrl: string | null;
  claudeConfig: object | null;
}

export default function McpPage() {
  const [authData, setAuthData] = useState<McpAuthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('cherrystudio');
  const t = useTranslations('mcp');

  // 自动生成 API Key
  const autoGenerateApiKey = useCallback(async () => {
    try {
      const response = await fetch('/api/mcp-auth', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setAuthData({
          hasApiKey: true,
          apiKey: data.apiKey,
          maskedApiKey: data.maskedApiKey,
          connectionUrl: data.connectionUrl,
          claudeConfig: data.claudeConfig,
        });
      } else {
        toast.error(t('messages.autoGenerateFailed'));
        // 如果自动生成失败，设置为无 API Key 状态
        setAuthData({
          hasApiKey: false,
          apiKey: null,
          maskedApiKey: null,
          connectionUrl: null,
          claudeConfig: null,
        });
      }
    } catch (error) {
      console.error('Error auto-generating API key:', error);
      toast.error(t('messages.autoGenerateFailed'));
      // 如果自动生成失败，设置为无 API Key 状态
      setAuthData({
        hasApiKey: false,
        apiKey: null,
        maskedApiKey: null,
        connectionUrl: null,
        claudeConfig: null,
      });
    }
  }, [t]);

  // 获取 MCP 认证信息
  const fetchAuthData = useCallback(async () => {
    try {
      const response = await fetch('/api/mcp-auth');
      if (response.ok) {
        const data = await response.json();

        // 如果用户没有 API Key，自动生成一个
        if (!data.hasApiKey) {
          await autoGenerateApiKey();
        } else {
          setAuthData(data);
        }
      } else {
        toast.error(t('messages.fetchFailed'));
      }
    } catch (error) {
      console.error('Error fetching auth data:', error);
      toast.error(t('messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  }, [t, autoGenerateApiKey]);

  // 生成新的 API Key
  const generateApiKey = async () => {
    if (!confirm(t('messages.confirmRegenerate'))) {
      return;
    }
    setGenerating(true);
    try {
      const response = await fetch('/api/mcp-auth', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setAuthData({
          hasApiKey: true,
          apiKey: data.apiKey,
          maskedApiKey: data.maskedApiKey,
          connectionUrl: data.connectionUrl,
          claudeConfig: data.claudeConfig,
        });
        toast.success(t('messages.generateSuccess'));
      } else {
        toast.error(t('messages.generateFailed'));
      }
    } catch (error) {
      console.error('Error generating API key:', error);
      toast.error(t('messages.generateFailed'));
    } finally {
      setGenerating(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t('messages.copySuccess', { label }));
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error(t('messages.copyFailed'));
    }
  };

  useEffect(() => {
    fetchAuthData();
  }, [fetchAuthData]);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <CardLoading text={t('loading')} />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div>
        <h1 className="text-xl font-bold">{t('title')}</h1>
      </div>
      {/* 如果没有 API Key，显示生成中状态 */}
      {authData && !authData.hasApiKey && (
        <Card>
          <CardHeader>
            <CardTitle>{t('connectionInfo')}</CardTitle>
            <CardDescription>
              {t('autoGenerating')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertDescription>
                {t('autoGeneratingDescription')}
              </AlertDescription>
            </Alert>
            <ButtonLoading text={t('generating')} />
          </CardContent>
        </Card>
      )}

      {/* 连接信息卡片 */}
      {authData?.hasApiKey && authData.connectionUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {t('connectionInfo')}
            </CardTitle>
            <CardDescription>
              {t('connectionDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('mcpServer')}</label>
              <div className="flex items-center gap-2">
                <code className="flex-1 p-2 bg-muted rounded text-sm font-mono break-all">
                  {authData.connectionUrl}
                </code>

              </div>
            </div>
            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                onClick={() => copyToClipboard(authData.connectionUrl || '', t('mcpServer'))}
              >
                <Copy className="h-4 w-4" />
                {t('copy')}
              </Button>
              <Button
                variant="outline"
                onClick={generateApiKey}
                disabled={generating}
              >
                {generating ? (
                  <ButtonLoading text={t('regenerating')} />
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4" />
                    {t('regenerate')}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用指引模块 */}
      {authData?.hasApiKey && (
        <Card>
          <CardHeader>
            <CardTitle>{t('usageGuide')}</CardTitle>
            <CardDescription>
              {t('usageGuideDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 标签页导航 */}
            <div className="flex space-x-1 border-b">
              <button
                onClick={() => setActiveTab('cherrystudio')}
                className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${activeTab === 'cherrystudio'
                  ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                  : 'text-muted-foreground hover:text-foreground cursor-pointer'
                  }`}
              >
                CherryStudio
              </button>
              <button
                onClick={() => setActiveTab('deepchat')}
                className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${activeTab === 'deepchat'
                  ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                  : 'text-muted-foreground hover:text-foreground cursor-pointer'
                  }`}
              >
                DeepChat
              </button>
              <button
                onClick={() => setActiveTab('claude-desktop')}
                className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${activeTab === 'claude-desktop'
                  ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                  : 'text-muted-foreground hover:text-foreground cursor-pointer'
                  }`}
              >
                Claude Desktop
              </button>
            </div>

            {/* 标签页内容 */}
            <div className="mt-4">
              {activeTab === 'claude-desktop' && authData.claudeConfig && (
                <div className="space-y-6">
                  {/* Claude Desktop 配置部分 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">{t('claude.title')}</h3>
                    <div className="space-y-2">
                    <h4 className="font-medium">{t('claude.step1')}</h4>
                      <p className="text-sm text-muted-foreground ml-4">
                        {t('claude.step1Description')}
                      </p>
                      <pre className="p-4 bg-muted rounded text-sm font-mono overflow-x-auto ml-4">
                        {JSON.stringify(authData.claudeConfig, null, 2)}
                      </pre>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className='ml-4'
                        size='sm'
                        onClick={() => copyToClipboard(
                          JSON.stringify(authData.claudeConfig, null, 2),
                          t('claude.copyConfig')
                        )}
                      >
                        <Copy className="h-4 w-4" />
                        {t('claude.copyConfig')}
                      </Button>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium">{t('claude.step2')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('claude.step2Description')}
                        </p>
                        <Image
                          src='/images/doc/claude01.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">{t('claude.step3')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('claude.step3Description')}
                        </p>
                        <Image
                          src='/images/doc/claude02.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'deepchat' && (
                <div className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">{t('deepchat.title')}</h3>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium">{t('deepchat.step1')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('deepchat.step1Description')}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">{t('deepchat.step2')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('deepchat.step2Description')}
                        </p>
                        <Image
                          src='/images/doc/deepchat01.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">{t('deepchat.step3')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('deepchat.step3Description')}
                        </p>
                        <Image
                          src='/images/doc/deepchat02.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'cherrystudio' && (
                <div className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">{t('cherrystudio.title')}</h3>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium">{t('cherrystudio.step1')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('cherrystudio.step1Description')}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">{t('cherrystudio.step2')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('cherrystudio.step2Description')}
                        </p>
                        <Image
                          src='/images/doc/cherryStudio01.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">{t('cherrystudio.step3')}</h4>
                        <p className="text-sm text-muted-foreground mt-2 ml-4">
                          {t('cherrystudio.step3Description')}
                        </p>
                        <Image
                          src='/images/doc/cherryStudio02.png'
                          alt="deepchat"
                          width={800}
                          height={800}
                          className='mt-2 ml-4'
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
