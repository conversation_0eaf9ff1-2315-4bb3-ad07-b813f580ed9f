import { notFound } from 'next/navigation';
import { getPublicPromptById } from '@/lib/explore';
import { PublicPromptDetail } from '@/components/explore/public-prompt-detail';
import type { Metadata } from 'next';
import { cache } from 'react';

export const dynamic = 'force-dynamic';

interface ExplorePromptDetailPageProps {
  params: Promise<{ id: string }>;
}

// 缓存的数据获取函数，避免重复查询
const getCachedPublicPromptById = cache(async (id: number) => {
  return getPublicPromptById(id);
});

// 生成动态页面元数据
export async function generateMetadata({ params }: ExplorePromptDetailPageProps): Promise<Metadata> {
  try {
    const { id } = await params;
    const promptId = parseInt(id, 10);

    if (isNaN(promptId)) {
      return {
        title: 'Prompt 不存在 - Prompt Repo',
      };
    }

    const prompt = await getCachedPublicPromptById(promptId);

    if (!prompt) {
      return {
        title: 'Prompt 不存在 - Prompt Repo',
      };
    }

    return {
      title: `${prompt.name} - Prompt Repo`,
      description: prompt.description || `查看 ${prompt.name} 的详细信息和内容`,
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: '加载失败 - Prompt Repo',
    };
  }
}

export default async function ExplorePromptDetailPage({ params }: ExplorePromptDetailPageProps) {
  const { id } = await params;
  const promptId = parseInt(id, 10);

  if (isNaN(promptId)) {
    notFound();
  }

  try {
    const prompt = await getCachedPublicPromptById(promptId);

    if (!prompt) {
      notFound();
    }

    return <PublicPromptDetail prompt={prompt} />;
  } catch (error) {
    console.error('Error loading prompt detail:', error);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载 Prompt 详情，请稍后重试</p>
        </div>
      </div>
    );
  }
}
