import { notFound } from 'next/navigation';
import { getPublicPrompts, getCategoriesWithPublicPromptCount, getCachedCategoryById } from '@/lib/explore';
import { ExplorePage } from '@/components/explore/explore-page';
import type { Metadata } from 'next';

export const dynamic = 'force-dynamic';

interface ExploreCategoryPageProps {
  params: Promise<{ categoryId: string }>;
}

// 生成动态页面元数据
export async function generateMetadata({ params }: ExploreCategoryPageProps): Promise<Metadata> {
  try {
    const { categoryId } = await params;
    const categoryIdNum = parseInt(categoryId, 10);

    if (isNaN(categoryIdNum)) {
      return {
        title: '分类未找到 - Prompt Repo',
        description: '请求的分类不存在',
      };
    }

    const category = await getCachedCategoryById(categoryIdNum);

    if (!category) {
      return {
        title: '分类未找到 - Prompt Repo',
        description: '请求的分类不存在',
      };
    }

    return {
      title: `${category.name} - Prompt Repo`,
      description: `浏览 ${category.name} 分类下的优质 Prompt 模板`,
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: '分类未找到 - Prompt Repo',
      description: '加载分类信息失败',
    };
  }
}

export default async function ExploreCategoryPage({ params }: ExploreCategoryPageProps) {
  try {
    const { categoryId } = await params;
    const categoryIdNum = parseInt(categoryId, 10);

    if (isNaN(categoryIdNum)) {
      notFound();
    }

    // 使用缓存的分类查询来验证分类是否存在（避免重复查询）
    const category = await getCachedCategoryById(categoryIdNum);
    if (!category) {
      notFound();
    }

    // 获取初始数据
    const [initialData, categories] = await Promise.all([
      getPublicPrompts(
        { page: 1, limit: 12 },
        { categoryId: categoryIdNum }
      ),
      getCategoriesWithPublicPromptCount(),
    ]);

    return (
      <ExplorePage
        initialData={initialData}
        categories={categories}
        currentCategoryId={categoryIdNum}
      />
    );
  } catch (error) {
    console.error('Error loading explore category page:', error);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载分类页面，请稍后重试</p>
        </div>
      </div>
    );
  }
}
