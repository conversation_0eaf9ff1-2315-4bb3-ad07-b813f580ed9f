import { getPublicPrompts, getCategoriesWithPublicPromptCount } from '@/lib/explore';
import { ExplorePage } from '@/components/explore/explore-page';
import type { Metadata } from 'next';

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: '发现 Prompt - Prompt Repo',
    description: '探索社区分享的优质 Prompt 模板',
  };
} 

export default async function ExploreMainPage() {
  try {
    // 获取初始数据
    const [initialData, categories] = await Promise.all([
      getPublicPrompts({ page: 1, limit: 12 }),
      getCategoriesWithPublicPromptCount(),
    ]);

    return (
      <ExplorePage 
        initialData={initialData} 
        categories={categories}
      />
    );
  } catch (error) {
    console.error('Error loading explore page:', error);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载发现页面，请稍后重试</p>
        </div>
      </div>
    );
  }
}
