'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Trash2  } from 'lucide-react';
import { toast } from 'sonner';
import { useChatStore } from '@/lib/stores/chat-store';

export default function ChatSettingsPage() {
  const router = useRouter();
  const [deleteAllDialogOpen, setDeleteAllDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const { deleteAllChats } = useChatStore();

  const handleDeleteAllChats = async () => {
    setIsDeleting(true);
    try {
      await deleteAllChats();
      toast.success('所有对话已删除');
      setDeleteAllDialogOpen(false);
      // 跳转回聊天主页面
      router.push('/chat');
    } catch (error) {
      console.error('删除所有对话失败:', error);
      toast.error('删除失败，请重试');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-2xl mx-auto px-4">
        <Card>
          <CardHeader>
            <CardTitle>对话设置</CardTitle>
            <CardDescription>
              管理您的对话记录和相关设置
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Button
              variant="outline"
              onClick={() => setDeleteAllDialogOpen(true)}
              className="gap-2"
            >
              <Trash2 className="w-4 h-4" />
              删除全部对话
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={deleteAllDialogOpen} onOpenChange={setDeleteAllDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              确认删除所有对话
            </DialogTitle>
            <DialogDescription className="text-left space-y-2">
              <p>您即将删除所有对话记录，包括：</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>所有对话历史</li>
                <li>所有消息内容</li>
              </ul>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteAllDialogOpen(false)}
              disabled={isDeleting}
            >
              取消
            </Button>
            <Button
              variant="default"
              onClick={handleDeleteAllChats}
              disabled={isDeleting}
              className="gap-2"
            >
              {isDeleting ? (
                <>删除中...</>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  确认删除
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
