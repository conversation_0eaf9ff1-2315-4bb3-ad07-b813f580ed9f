'use client';

import { useState, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ChatSidebar } from '@/components/chat/chat-sidebar';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import { ArtifactSidebar } from '@/components/chat/artifact-sidebar';
import { useArtifact } from '@/hooks/use-artifact';
// 注意：现在使用 Zustand store 管理状态，不再需要 ChatProvider

interface ChatLayoutProps {
  children: React.ReactNode;
}

function ChatLayoutContent({ children }: ChatLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Artifact 状态管理
  const { isOpen, currentArtifact, activeTab, closeArtifact, setActiveTab } = useArtifact();

  // 从路径中提取当前聊天ID
  const getCurrentChatId = useCallback(() => {
    const match = pathname.match(/^\/chat\/([^\/]+)$/);
    return match ? match[1] : undefined;
  }, [pathname]);

  const currentChatId = getCurrentChatId();

  return (
    <div className="h-[calc(100vh-4rem)] flex relative overflow-hidden">
      {/* 桌面端侧边栏 */}
      <div className="hidden md:block">
        <ChatSidebar
          currentChatId={currentChatId}
        />
      </div>

      {/* 移动端侧边栏遮罩 */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* 移动端侧边栏 */}
      <div className={`
        fixed left-0 top-16 h-[calc(100vh-4rem)] z-50 md:hidden
        transform transition-transform duration-300 ease-in-out
        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <ChatSidebar
          currentChatId={currentChatId}
          onChatSelect={() => setIsMobileSidebarOpen(false)}
        />
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col min-w-0 h-full">
        {/* 移动端顶部栏 */}
        <div className="md:hidden flex items-center justify-between p-4 border-b bg-background flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileSidebarOpen(true)}
            className="flex items-center gap-2"
          >
            <Menu className="w-4 h-4" />
            对话历史
          </Button>
          {currentChatId && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/chat')}
            >
              新建对话
            </Button>
          )}
        </div>

        {/* 聊天内容区域 */}
        <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
          {children}
        </div>
      </div>

      {/* Artifact 侧边栏 */}
      <ArtifactSidebar
        isOpen={isOpen}
        artifact={currentArtifact}
        activeTab={activeTab}
        onClose={closeArtifact}
        onTabChange={setActiveTab}
      />
    </div>
  );
}

export default function ChatLayout({ children }: ChatLayoutProps) {
  return <ChatLayoutContent>{children}</ChatLayoutContent>;
}
