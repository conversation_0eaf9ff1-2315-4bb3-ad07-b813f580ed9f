import { requireAuth } from '@/lib/auth-server';
import { ChatContent } from '@/components/chat/chat-content';
import { notFound } from 'next/navigation';

interface ChatPageProps {
  params: Promise<{
    chatId: string;
  }>;
}

export default async function ChatPage({ params }: ChatPageProps) {
  // 验证用户身份
  await requireAuth();

  const { chatId } = await params;

  // 验证 chatId 是否为有效字符串
  if (!chatId || typeof chatId !== 'string') {
    notFound();
  }

  return <ChatContent initialChatId={chatId} />;
}
