import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, Users, Shield, AlertTriangle, Scale, Settings, MessageCircle, CheckCircle } from 'lucide-react';

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800 hover:bg-blue-100">
            <Scale className="w-4 h-4 mr-2" />
            服务条款
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            服务条款
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            欢迎使用 Prompt Repo，请仔细阅读以下服务条款，使用我们的服务即表示您同意遵守这些条款
          </p>
          <div className="mt-6 text-sm text-gray-500">
            最后更新时间：2025年5月31日
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* 服务概述 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">服务概述</CardTitle>
                  <CardDescription>了解我们提供的服务内容和范围</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                Prompt Repo 是一个免费的 SaaS 服务平台，为用户提供 Prompt 管理功能。通过我们的平台，您可以：
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">创建、编辑和管理大语言模型的 Prompt 提示词</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">通过 MCP（Model Context Protocol）协议调用自定义 Prompts</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">在各种 AI 客户端中便捷使用您的 Prompt 库</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">享受完全免费的服务体验</span>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                我们致力于为用户提供高效、便捷的 Prompt 管理体验，提升 AI 交互的效率和质量。
              </p>
            </CardContent>
          </Card>

          {/* 用户责任 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">用户责任</CardTitle>
                  <CardDescription>使用我们服务时您需要承担的责任和义务</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">账户安全：</h3>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">账户保护：</strong>
                      <span className="text-gray-700">您有责任保护您的账户信息和登录凭据的安全</span>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">授权使用：</strong>
                      <span className="text-gray-700">您对通过您账户进行的所有活动承担责任</span>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">及时通知：</strong>
                      <span className="text-gray-700">如发现账户被盗用，请立即通知我们</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">内容责任：</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">确保您创建的 Prompt 内容合法合规</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">不得上传包含违法、有害或侵权内容的 Prompt</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">尊重他人的知识产权和隐私权</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">对您发布的内容承担全部法律责任</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 服务使用规则 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">服务使用规则</CardTitle>
                  <CardDescription>为了维护良好的服务环境，请遵守以下规则</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="w-5 h-5 text-green-600" />
                  <strong className="text-green-800">合理使用</strong>
                </div>
                <p className="text-green-700">
                  请合理使用我们的服务，避免对系统造成过度负担或影响其他用户的正常使用。
                </p>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">禁止行为：</h3>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <ul className="text-red-700 space-y-2">
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>恶意攻击、破坏或干扰我们的服务</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>尝试未经授权访问其他用户的账户或数据</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>上传病毒、恶意代码或其他有害内容</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>进行任何可能损害服务安全性的活动</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>违反适用的法律法规或第三方权利</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">违规处理：</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">警告措施</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 发送违规通知</li>
                      <li>• 要求整改违规内容</li>
                      <li>• 限制部分功能使用</li>
                    </ul>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">严重违规</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 暂停账户使用</li>
                      <li>• 删除违规内容</li>
                      <li>• 永久封禁账户</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 知识产权 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">知识产权</CardTitle>
                  <CardDescription>关于平台和用户内容的知识产权说明</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">平台知识产权：</h3>
                <div className="space-y-3">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <strong className="text-gray-900">平台所有权：</strong>
                    <p className="text-gray-700 mt-1">Prompt Repo 平台的设计、代码、商标等知识产权归我们所有</p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <strong className="text-gray-900">使用许可：</strong>
                    <p className="text-gray-700 mt-1">我们授予您有限的、非独占的使用许可来访问和使用我们的服务</p>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <strong className="text-gray-900">限制条件：</strong>
                    <p className="text-gray-700 mt-1">您不得复制、修改、分发或商业化使用我们的平台技术</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">用户内容权利：</h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <ul className="text-blue-700 space-y-2">
                    <li className="flex items-center space-x-2">
                      <span className="text-blue-500">✓</span>
                      <span>您保留对自己创建的 Prompt 内容的所有权</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-blue-500">✓</span>
                      <span>您可以自由导出、修改和删除您的 Prompt</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-blue-500">✓</span>
                      <span>我们不会声称对您的原创内容拥有所有权</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-blue-500">✓</span>
                      <span>您授予我们提供服务所需的技术性使用许可</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 免责声明 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">免责声明</CardTitle>
                  <CardDescription>服务使用的风险和责任限制</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <strong className="text-yellow-800">重要提醒</strong>
                </div>
                <p className="text-yellow-700">
                  我们的服务按&ldquo;现状&rdquo;提供，我们不对服务的完整性、准确性或可靠性做出任何明示或暗示的保证。
                </p>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">服务限制：</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-gray-500 rounded-full"></div>
                    <span className="text-gray-700">服务可能因维护、升级或技术问题而中断</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-gray-500 rounded-full"></div>
                    <span className="text-gray-700">我们不保证服务的持续可用性或无错误运行</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-gray-500 rounded-full"></div>
                    <span className="text-gray-700">第三方服务的集成可能受到外部因素影响</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">责任限制：</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 text-sm">
                    在法律允许的最大范围内，我们不对因使用或无法使用我们的服务而导致的任何直接、间接、偶然、特殊或后果性损害承担责任，包括但不限于数据丢失、业务中断或利润损失。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 服务变更 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <Settings className="w-5 h-5 text-indigo-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">服务变更和终止</CardTitle>
                  <CardDescription>关于服务修改、暂停或终止的说明</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">服务变更：</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">我们可能会不时更新、修改或改进我们的服务</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">重大变更将提前通知用户</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">继续使用服务即表示接受变更</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">服务终止：</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">用户终止</h4>
                    <p className="text-blue-700 text-sm mb-2">您可以随时停止使用我们的服务</p>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• 删除您的账户</li>
                      <li>• 导出您的数据</li>
                      <li>• 停止访问平台</li>
                    </ul>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4">
                    <h4 className="font-semibold text-red-900 mb-2">我们的终止权</h4>
                    <p className="text-red-700 text-sm mb-2">在以下情况下我们可能终止服务</p>
                    <ul className="text-sm text-red-700 space-y-1">
                      <li>• 严重违反服务条款</li>
                      <li>• 长期不活跃的账户</li>
                      <li>• 服务停止运营</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 争议解决 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Scale className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">争议解决</CardTitle>
                  <CardDescription>处理争议和法律适用的相关条款</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">解决方式：</h3>
                <div className="space-y-3">
                  <div className="border-l-4 border-green-500 pl-4">
                    <strong className="text-gray-900">友好协商：</strong>
                    <p className="text-gray-700 mt-1">我们鼓励通过友好协商的方式解决任何争议或分歧</p>
                  </div>
                  <div className="border-l-4 border-blue-500 pl-4">
                    <strong className="text-gray-900">客服支持：</strong>
                    <p className="text-gray-700 mt-1">您可以通过我们的客服渠道寻求帮助和解决方案</p>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <strong className="text-gray-900">法律途径：</strong>
                    <p className="text-gray-700 mt-1">如协商无果，可通过适用法律规定的途径解决</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">适用法律：</h3>
                <p className="text-gray-700 text-sm">
                  本服务条款受中华人民共和国法律管辖。任何争议应首先通过友好协商解决，协商不成的，应提交至服务提供方所在地有管辖权的人民法院解决。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 条款更新 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl">条款更新</CardTitle>
              <CardDescription>服务条款的修改和生效说明</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                我们可能会根据法律要求、业务发展或服务改进的需要更新本服务条款。当我们进行重要更改时，我们会：
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">在网站显著位置发布更新通知</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">通过邮件通知注册用户</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">提供合理的过渡期</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">更新生效日期</span>
                </div>
              </div>
              <p className="text-gray-700">
                更新后的条款发布后继续使用我们的服务，即表示您接受新的服务条款。如果您不同意更新后的条款，请停止使用我们的服务。
              </p>
            </CardContent>
          </Card>

          {/* 联系我们 */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">联系我们</CardTitle>
                  <CardDescription>如有服务条款相关问题，请随时联系我们</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                如果您对本服务条款有任何疑问，或需要相关帮助和支持，请通过以下方式联系我们：
              </p>
              <div className="bg-white rounded-lg p-4 border">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <strong className="text-gray-900">邮箱：</strong>
                    <span className="text-blue-600"><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <strong className="text-gray-900">响应时间：</strong>
                    <span className="text-gray-700">我们将在 7 个工作日内回复您的询问</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <strong className="text-gray-900">服务时间：</strong>
                    <span className="text-gray-700">周一至周五 9:00-18:00（北京时间）</span>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                我们重视您的意见和建议，并致力于及时、专业地处理您的所有服务相关请求。
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 底部声明 */}
        <div className="text-center mt-12 pt-8 border-t border-gray-200">
          <p className="text-gray-600">
            本服务条款构成您与 Prompt Repo 之间的完整协议
          </p>
          <p className="text-sm text-gray-500 mt-2">
            感谢您选择 Prompt Repo，我们将竭诚为您提供优质的服务体验
          </p>
        </div>
      </div>
    </div>
  );
}
