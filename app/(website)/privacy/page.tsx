import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, Lock, Eye, Users, Database, FileText } from 'lucide-react';

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800 hover:bg-blue-100">
            <Shield className="w-4 h-4 mr-2" />
            隐私保护
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            隐私政策
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们致力于保护您的隐私和数据安全，以下是我们的隐私保护承诺和具体措施
          </p>
          <div className="mt-6 text-sm text-gray-500">
            最后更新时间：2025年5月31日
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* 服务概述 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">服务概述</CardTitle>
                  <CardDescription>了解我们提供的服务内容</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                Prompt Repo 是一个为用户提供 Prompt 管理的 SaaS 服务平台。用户可以通过我们的平台手动创建、编辑、管理和使用大语言模型的 Prompt 提示词，并通过 MCP（Model Context Protocol）协议在各种 AI 客户端中便捷调用这些自定义 Prompts。
              </p>
              <p className="text-gray-700 leading-relaxed">
                我们的服务完全免费，旨在为用户提供高效、便捷的 Prompt 管理体验，提升 AI 交互的效率和质量。
              </p>
            </CardContent>
          </Card>

          {/* 数据收集 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Database className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">数据收集</CardTitle>
                  <CardDescription>我们收集哪些信息以及收集的目的</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">我们收集的信息类型：</h3>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">账户信息：</strong>
                      <span className="text-gray-700">包括您的用户名、邮箱地址等基本注册信息</span>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">Prompt 内容：</strong>
                      <span className="text-gray-700">您创建和编辑的 Prompt 提示词内容、参数配置等</span>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-gray-900">使用数据：</strong>
                      <span className="text-gray-700">服务使用记录、访问日志等技术信息</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">收集目的：</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">提供和维护我们的服务功能</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">改进用户体验和服务质量</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">确保服务安全和防止滥用</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">提供技术支持和客户服务</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据保护 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Lock className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">数据保护</CardTitle>
                  <CardDescription>我们如何保护您的数据安全</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="w-5 h-5 text-green-600" />
                  <strong className="text-green-800">核心承诺</strong>
                </div>
                <p className="text-green-700">
                  在未经您明确授权的情况下，我们绝不会将您的个人数据和 Prompt 内容分享给任何第三方。
                </p>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">安全措施：</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">技术保护</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 数据传输加密（HTTPS/TLS）</li>
                      <li>• 数据库访问控制</li>
                      <li>• 定期安全审计</li>
                      <li>• 备份和灾难恢复</li>
                    </ul>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">管理保护</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 员工数据访问权限控制</li>
                      <li>• 隐私保护培训</li>
                      <li>• 数据处理流程规范</li>
                      <li>• 安全事件响应机制</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据使用 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Eye className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">数据使用</CardTitle>
                  <CardDescription>我们如何使用您的数据</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900">数据使用原则：</h3>
                <div className="space-y-3">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <strong className="text-gray-900">最小化原则：</strong>
                    <p className="text-gray-700 mt-1">我们只收集和使用提供服务所必需的最少数据</p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <strong className="text-gray-900">目的限制：</strong>
                    <p className="text-gray-700 mt-1">数据仅用于明确告知的目的，不会用于其他用途</p>
                  </div>
                  <div className="border-l-4 border-purple-500 pl-4">
                    <strong className="text-gray-900">透明度：</strong>
                    <p className="text-gray-700 mt-1">我们会清楚地告知您数据的使用方式和目的</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">不会进行的数据使用：</h3>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <ul className="text-red-700 space-y-2">
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>出售或出租您的个人数据</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>未经授权与第三方分享您的 Prompt 内容</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>用于与服务无关的商业目的</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="text-red-500">✗</span>
                      <span>进行侵犯隐私的数据挖掘或分析</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 用户权利 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-indigo-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">您的权利</CardTitle>
                  <CardDescription>您对自己数据拥有的控制权</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">访问权</h4>
                    <p className="text-blue-700 text-sm">您有权了解我们收集了您的哪些数据</p>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-900 mb-2">修改权</h4>
                    <p className="text-green-700 text-sm">您可以随时修改或更新您的个人信息</p>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h4 className="font-semibold text-purple-900 mb-2">删除权</h4>
                    <p className="text-purple-700 text-sm">您可以要求删除您的账户和相关数据</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-900 mb-2">导出权</h4>
                    <p className="text-yellow-700 text-sm">您可以导出您创建的所有 Prompt 数据</p>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4">
                    <h4 className="font-semibold text-red-900 mb-2">撤回同意权</h4>
                    <p className="text-red-700 text-sm">您可以随时撤回对数据处理的同意</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">投诉权</h4>
                    <p className="text-gray-700 text-sm">如有隐私问题，您可以向我们投诉</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cookie 和追踪 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl">Cookie 和追踪技术</CardTitle>
              <CardDescription>我们如何使用 Cookie 和类似技术</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                我们使用 Cookie 和类似技术来改善您的使用体验，包括：
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700">保持您的登录状态</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700">记住您的偏好设置</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700">分析网站使用情况以改进服务</span>
                </div>
              </div>
              <p className="text-gray-700">
                您可以通过浏览器设置控制 Cookie 的使用，但这可能会影响某些功能的正常使用。
              </p>
            </CardContent>
          </Card>

          {/* 政策更新 */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl">政策更新</CardTitle>
              <CardDescription>隐私政策的变更和通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                我们可能会不时更新本隐私政策，以反映我们服务的变化或法律要求的调整。当我们进行重要更改时，我们会：
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">在网站上发布更新的政策</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">通过邮件通知注册用户</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700">在显著位置标明更新日期</span>
                </div>
              </div>
              <p className="text-gray-700">
                继续使用我们的服务即表示您接受更新后的隐私政策。
              </p>
            </CardContent>
          </Card>

          {/* 联系我们 */}
          <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
            <CardHeader>
              <CardTitle className="text-2xl">联系我们</CardTitle>
              <CardDescription>如有隐私相关问题，请随时联系我们</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                如果您对本隐私政策有任何疑问，或需要行使您的数据权利，请通过以下方式联系我们：
              </p>
              <div className="bg-white rounded-lg p-4 border">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <strong className="text-gray-900">邮箱：</strong>
                    <span className="text-blue-600"><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <strong className="text-gray-900">响应时间：</strong>
                    <span className="text-gray-700">我们将在 7 个工作日内回复您的询问</span>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                我们重视您的隐私权利，并致力于及时、专业地处理您的所有隐私相关请求。
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 底部声明 */}
        <div className="text-center mt-12 pt-8 border-t border-gray-200">
          <p className="text-gray-600">
            本隐私政策是我们对用户隐私保护承诺的重要组成部分
          </p>
          <p className="text-sm text-gray-500 mt-2">
            感谢您信任 Prompt Repo，我们将持续努力保护您的隐私和数据安全
          </p>
        </div>
      </div>
    </div>
  );
}
