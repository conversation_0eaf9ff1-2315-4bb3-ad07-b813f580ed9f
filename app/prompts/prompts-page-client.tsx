'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { Download, Plus, Upload } from 'lucide-react';
import { PromptList } from '@/components/prompts/prompt-list';
import { PageLoading } from '@/components/ui/loading';
import type { PromptWithParameters } from '@/types';
import { fetchPrompts, exportPrompts, importPrompts } from '@/lib/api';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';

export function PromptsPageClient() {
  const t = useTranslations();
  const [prompts, setPrompts] = useState<PromptWithParameters[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const loadPrompts = useCallback(async (showRefreshToast = false) => {
    try {
      if (showRefreshToast) {
        setIsRefreshing(true);
      }

      const data = await fetchPrompts();
      setPrompts(data);
    } catch (error) {
      console.error('Error loading prompts:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadPrompts();
  }, [loadPrompts]);

  const handlePromptDeleted = () => {
    loadPrompts();
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      await exportPrompts();
      toast.success(t('prompts.exportSuccess'));
    } catch (error) {
      console.error('Error exporting prompts:', error);
      toast.error(t('common.errorOccurred'));
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    // 验证文件类型
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
      toast.error(t('prompts.invalidFileType'));
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }
    
    try {
      setIsImporting(true);
      const result = await importPrompts(file);
      toast.success(t('prompts.importSuccess', { count: result.importedCount }));
      
      // 重新加载提示列表
      loadPrompts(true);
    } catch (error: any) {
      console.error('Error importing prompts:', error);
      toast.error(error.message || t('common.errorOccurred'));
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <PageLoading text={t('common.loading')} />
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center justify-between mb-8">
        <div className="hidden sm:block">
          <h1 className="text-xl font-bold text-gray-900">{t('prompts.title')}</h1>
        </div>

        <div className="flex space-x-3">
          <input
            type="file"
            ref={fileInputRef}
            accept=".json,application/json"
            onChange={handleFileChange}
            className="hidden"
          />
          <Button
            variant="outline"
            onClick={handleImportClick}
            disabled={isImporting}
          >
            <Download className="w-4 h-4" />
            {isImporting ? t('common.importing') : t('prompts.import')}
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={isExporting}
          >
            <Upload className="w-4 h-4" />
            {isExporting ? t('common.exporting') : t('prompts.export')}
          </Button>
          <Link href="/prompts/create">
            <Button>
              <Plus className="w-4 h-4" />
              {t('prompts.create')}
            </Button>
          </Link>
        </div>
      </div>
      <PromptList prompts={prompts} onPromptDeleted={handlePromptDeleted} />
    </>
  );
}
