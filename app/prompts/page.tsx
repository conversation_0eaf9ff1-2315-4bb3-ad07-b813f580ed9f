import { requireAuth } from '@/lib/auth-server';
import { PromptsPageClient } from '@/app/prompts/prompts-page-client';

// 强制页面为动态渲染，因为需要读取 headers 进行身份验证
export const dynamic = 'force-dynamic';

export default async function PromptsPage() {
  // 服务器端认证检查，如果未登录会自动重定向到登录页
  await requireAuth('/prompts');
  return (
    <div className="container mx-auto px-4 py-8">
      <PromptsPageClient />
    </div>
  );
}
