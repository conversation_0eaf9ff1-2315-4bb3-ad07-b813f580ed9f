import { notFound } from 'next/navigation';
import { PromptForm } from '@/components/prompts/prompt-form';
import { getPromptById } from '@/lib/prompts';
import { requireAuth } from '@/lib/auth-server';

// 强制动态渲染，因为使用了 headers()
export const dynamic = 'force-dynamic';

interface EditPromptPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditPromptPage({ params }: EditPromptPageProps) {
  const { id } = await params;
  const promptId = parseInt(id, 10);

  if (isNaN(promptId)) {
    notFound();
  }

  // 验证用户身份，如果未登录会自动重定向到登录页面
  const session = await requireAuth(`/prompts/edit/${id}`);

  const prompt = await getPromptById(promptId, session.user.id);

  if (!prompt) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <PromptForm mode="edit" initialData={prompt} />
    </div>
  );
}
