import { PromptForm } from '@/components/prompts/prompt-form';
import { requireAuth } from '@/lib/auth-server';

// 强制动态渲染，因为使用了 headers()
export const dynamic = 'force-dynamic';

export default async function CreatePromptPage() {
  // 验证用户身份，如果未登录会自动重定向到登录页面
  await requireAuth('/prompts/create');

  return (
    <div className="container mx-auto px-4 py-8">
      <PromptForm mode="create" />
    </div>
  );
}
