import { notFound } from 'next/navigation';
import { PromptDetail } from '@/components/prompts/prompt-detail';
import { getPromptById } from '@/lib/prompts';
import { requireAuth } from '@/lib/auth-server';

// 强制动态渲染，因为使用了 headers()
export const dynamic = 'force-dynamic';

interface PromptDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function PromptDetailPage({ params }: PromptDetailPageProps) {
  const { id } = await params;
  const promptId = parseInt(id, 10);

  if (isNaN(promptId)) {
    notFound();
  }

  // 验证用户身份，如果未登录会自动重定向到登录页面
  const session = await requireAuth(`/prompts/detail/${id}`);

  const prompt = await getPromptById(promptId, session.user.id);

  if (!prompt) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <PromptDetail prompt={prompt} />
    </div>
  );
}
