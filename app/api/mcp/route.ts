import { NextRequest, NextResponse } from 'next/server';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { toFetchResponse, toReqRes } from 'fetch-to-node';
import { createMcpServer } from '@/lib/mcp/server';
import { validateApiKey, extractApiKeyFromRequest } from '@/lib/mcp-auth';

// 处理 POST 请求 - 无状态模式
export async function POST(req: NextRequest) {
  // 在无状态模式下，为每个请求创建新的传输和服务器实例
  // 以确保完全隔离。单个实例会在多个客户端并发连接时导致请求 ID 冲突。

  try {
    // 验证 API Key
    const apiKey = extractApiKeyFromRequest(req);
    const validation = await validateApiKey(apiKey || '');

    if (!validation.isValid || !validation.user) {
      return NextResponse.json(
        {
          jsonrpc: '2.0',
          error: {
            code: -32001,
            message: `Unauthorized: ${validation.error || 'Invalid API key'}`,
          },
          id: null,
        },
        { status: 401 }
      );
    }

    // 获取请求体
    const body = await req.json();
    const method: string = body.method;
    if(method === 'prompts/get' && body.params.arguments === undefined) {
      body.params.arguments = {}
    }
    // 创建 MCP 服务器实例
    const server = await createMcpServer(validation.user.id, method);

    // 创建无状态传输实例
    const transport: StreamableHTTPServerTransport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined,
    });

    // 转换 Next.js 请求和响应对象为 Node.js 格式
    const { req: nodeReq, res: nodeRes } = toReqRes(req);

    // 请求关闭时清理资源
    nodeRes.on('close', () => {
      transport.close();
      server.close();
    });

    // 连接到 MCP 服务器
    await server.connect(transport);
    // 处理请求
    await transport.handleRequest(nodeReq, nodeRes, body);

    // 转换 Node.js 响应为 Next.js 响应
    return toFetchResponse(nodeRes);

  } catch (error) {
    console.error('Error handling MCP request:', error);
    return NextResponse.json(
      {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal server error',
        },
        id: null,
      },
      { status: 500 }
    );
  }
}

// 处理 GET 请求
export async function GET(req: NextRequest) {
  console.log('Received GET MCP request');
  return NextResponse.json(
    {
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: 'Method not allowed.',
      },
      id: null,
    },
    { status: 405 }
  );
}

// 设置 CORS 头部
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
