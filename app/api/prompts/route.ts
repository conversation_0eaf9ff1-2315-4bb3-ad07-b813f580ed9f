import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { getAllPrompts, createPrompt, checkPromptNameExists } from '@/lib/prompts';
import { validatePromptForm } from '@/lib/validations';
import { auth } from '@/lib/auth';
import type { PromptFormData } from '@/types';

// GET /api/prompts - 获取当前用户的所有 prompts
export async function GET() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const prompts = await getAllPrompts(session.user.id);
    return NextResponse.json(prompts);
  } catch (error) {
    console.error('Error fetching prompts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch prompts' },
      { status: 500 }
    );
  }
}

// POST /api/prompts - 创建新的 prompt
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const formData: PromptFormData = body;

    // 验证表单数据
    const validation = validatePromptForm(formData);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Validation failed', errors: validation.errors },
        { status: 400 }
      );
    }

    // 检查名称是否已存在（在用户范围内）
    const nameExists = await checkPromptNameExists(formData.name.trim(), session.user.id);
    if (nameExists) {
      return NextResponse.json(
        { error: 'Name already exists' },
        { status: 409 }
      );
    }

    // 创建 prompt
    const prompt = await createPrompt(formData, session.user.id);
    return NextResponse.json(prompt, { status: 201 });
  } catch (error) {
    console.error('Error creating prompt:', error);
    return NextResponse.json(
      { error: 'Failed to create prompt' },
      { status: 500 }
    );
  }
}
