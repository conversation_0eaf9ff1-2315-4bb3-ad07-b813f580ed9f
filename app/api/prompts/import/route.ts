import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { createPrompt, checkPromptNameExists } from '@/lib/prompts';
import { validatePromptForm } from '@/lib/validations';
import { auth } from '@/lib/auth';
import type { PromptFormData, ParameterFormData } from '@/types';

// 导入数据的类型定义
interface ImportParameterData {
  name: string;
  description?: string;
  required?: boolean;
}

interface ImportPromptData {
  name: string;
  description?: string;
  parameters?: ImportParameterData[];
  content: string;
  isPublic?: boolean;
  createdAt?: number;
  updatedAt?: number;
}

// POST /api/prompts/import - 导入 prompts
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // 验证导入数据是否为数组
    if (!Array.isArray(body)) {
      return NextResponse.json(
        { error: 'Invalid import format. Expected an array of prompts.' },
        { status: 400 }
      );
    }

    // 验证每个 prompt 的格式
    const validationErrors: string[] = [];
    for (let i = 0; i < body.length; i++) {
      const prompt = body[i] as ImportPromptData;
      
      // 检查必填字段
      if (!prompt.name) {
        validationErrors.push(`Prompt at index ${i}: Missing required field 'name'`);
      }
      
      if (!prompt.content) {
        validationErrors.push(`Prompt at index ${i}: Missing required field 'content'`);
      }
      
      // 验证参数格式
      if (prompt.parameters) {
        if (!Array.isArray(prompt.parameters)) {
          validationErrors.push(`Prompt at index ${i}: 'parameters' must be an array`);
        } else {
          for (let j = 0; j < prompt.parameters.length; j++) {
            const param = prompt.parameters[j];
            if (!param.name) {
              validationErrors.push(`Prompt at index ${i}, parameter at index ${j}: Missing required field 'name'`);
            }
          }
        }
      }
    }
    
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', errors: validationErrors },
        { status: 400 }
      );
    }

    // 导入 prompts
    const importedPrompts = [];
    const renamedPrompts: { original: string, renamed: string }[] = [];
    
    for (const promptData of body as ImportPromptData[]) {
      // 准备导入数据
      const formData: PromptFormData = {
        name: promptData.name,
        description: promptData.description || undefined,
        content: promptData.content,
        isPublic: promptData.isPublic || false,
        parameters: (promptData.parameters || []).map((param: ImportParameterData) => ({
          name: param.name,
          description: param.description || undefined,
          required: !!param.required
        }))
      };
      
      // 验证表单数据
      const validation = validatePromptForm(formData);
      if (!validation.isValid) {
        validationErrors.push(`Prompt '${formData.name}': ${validation.errors.map(e => e.message).join(', ')}`);
        continue;
      }
      
      // 检查名称是否已存在，如果存在则添加编号
      let nameExists = await checkPromptNameExists(formData.name.trim(), session.user.id);
      let counter = 0;
      let originalName = formData.name;
      
      while (nameExists) {
        counter++;
        formData.name = `${originalName}(${counter})`;
        nameExists = await checkPromptNameExists(formData.name.trim(), session.user.id);
      }
      
      if (counter > 0) {
        renamedPrompts.push({ original: originalName, renamed: formData.name });
      }
      
      // 创建 prompt
      const prompt = await createPrompt(formData, session.user.id);
      importedPrompts.push(prompt);
    }
    
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Some prompts failed validation', errors: validationErrors },
        { status: 400 }
      );
    }
    
    return NextResponse.json({
      success: true,
      importedCount: importedPrompts.length,
      renamedPrompts: renamedPrompts.length > 0 ? renamedPrompts : undefined
    }, { status: 201 });
  } catch (error) {
    console.error('Error importing prompts:', error);
    return NextResponse.json(
      { error: 'Failed to import prompts' },
      { status: 500 }
    );
  }
} 