import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { getAllPrompts } from '@/lib/prompts';
import { auth } from '@/lib/auth';

// GET /api/prompts/export - 导出用户的所有 prompts 为 JSON 文件
export async function GET() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取用户的所有 prompts
    const prompts = await getAllPrompts(session.user.id);
    
    // 转换为导出格式
    const exportData = prompts.map(prompt => ({
      name: prompt.name,
      description: prompt.description || '',
      inputSchema: prompt.parameters.map(param => ({
        name: param.name,
        description: param.description || '',
        required: param.required
      })),
      content: prompt.content,
      isPublic: prompt.isPublic,
      createdAt: prompt.createdAt.getTime(),
      updatedAt: prompt.updatedAt.getTime()
    }));

    // 设置响应头，使浏览器将响应视为下载文件
    const filename = `prompts-export-${new Date().toISOString().slice(0, 10)}.json`;
    
    return new NextResponse(JSON.stringify(exportData, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    });
  } catch (error) {
    console.error('Error exporting prompts:', error);
    return NextResponse.json(
      { error: 'Failed to export prompts' },
      { status: 500 }
    );
  }
} 