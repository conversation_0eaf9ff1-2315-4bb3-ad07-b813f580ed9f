import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { checkAdminPermission } from '@/lib/admin-auth'
import { db } from '@/db'
import { categories, prompts } from '@/db/schema'
import { eq, count, sql } from 'drizzle-orm'
import type { CategoryFormData, CategoryWithStats } from '@/types'

/**
 * 获取所有分类列表（包含统计信息）
 */
export async function GET() {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 获取分类列表及其 prompt 数量
    const categoriesWithStats = await db
      .select({
        id: categories.id,
        name: categories.name,
        createdAt: categories.createdAt,
        updatedAt: categories.updatedAt,
        promptCount: count(prompts.id),
      })
      .from(categories)
      .leftJoin(prompts, eq(categories.id, prompts.categoryId))
      .groupBy(categories.id, categories.name, categories.createdAt, categories.updatedAt)
      .orderBy(categories.name)

    const result: CategoryWithStats[] = categoriesWithStats.map(cat => ({
      id: cat.id,
      name: cat.name,
      createdAt: cat.createdAt,
      updatedAt: cat.updatedAt,
      promptCount: cat.promptCount,
    }))

    return NextResponse.json({
      success: true,
      data: result,
    })

  } catch (error) {
    console.error('获取分类列表失败:', error)
    return NextResponse.json(
      { error: '获取分类列表失败' },
      { status: 500 }
    )
  }
}

/**
 * 创建新分类
 */
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const formData: CategoryFormData = await request.json()

    // 验证数据
    if (!formData.name || formData.name.trim().length === 0) {
      return NextResponse.json(
        { error: '分类名称不能为空' },
        { status: 400 }
      )
    }

    const categoryName = formData.name.trim()

    // 检查分类名称是否已存在
    const existingCategory = await db
      .select()
      .from(categories)
      .where(eq(categories.name, categoryName))
      .limit(1)

    if (existingCategory.length > 0) {
      return NextResponse.json(
        { error: '分类名称已存在' },
        { status: 409 }
      )
    }

    // 创建分类
    const [newCategory] = await db
      .insert(categories)
      .values({
        name: categoryName,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning()

    return NextResponse.json({
      success: true,
      data: newCategory,
      message: '分类创建成功',
    })

  } catch (error) {
    console.error('创建分类失败:', error)
    return NextResponse.json(
      { error: '创建分类失败' },
      { status: 500 }
    )
  }
}
