import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { checkAdminPermission } from '@/lib/admin-auth'
import { db } from '@/db'
import { categories, prompts } from '@/db/schema'
import { eq, count } from 'drizzle-orm'
import type { CategoryFormData } from '@/types'

/**
 * 获取单个分类详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = await params
    const categoryId = parseInt(id)

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: '无效的分类 ID' },
        { status: 400 }
      )
    }

    // 获取分类信息及其 prompt 数量
    const [categoryWithStats] = await db
      .select({
        id: categories.id,
        name: categories.name,
        createdAt: categories.createdAt,
        updatedAt: categories.updatedAt,
        promptCount: count(prompts.id),
      })
      .from(categories)
      .leftJoin(prompts, eq(categories.id, prompts.categoryId))
      .where(eq(categories.id, categoryId))
      .groupBy(categories.id, categories.name, categories.createdAt, categories.updatedAt)

    if (!categoryWithStats) {
      return NextResponse.json(
        { error: '分类不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: categoryWithStats.id,
        name: categoryWithStats.name,
        createdAt: categoryWithStats.createdAt,
        updatedAt: categoryWithStats.updatedAt,
        promptCount: categoryWithStats.promptCount,
      },
    })

  } catch (error) {
    console.error('获取分类详情失败:', error)
    return NextResponse.json(
      { error: '获取分类详情失败' },
      { status: 500 }
    )
  }
}

/**
 * 更新分类
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = await params
    const categoryId = parseInt(id)

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: '无效的分类 ID' },
        { status: 400 }
      )
    }

    const formData: CategoryFormData = await request.json()

    // 验证数据
    if (!formData.name || formData.name.trim().length === 0) {
      return NextResponse.json(
        { error: '分类名称不能为空' },
        { status: 400 }
      )
    }

    const categoryName = formData.name.trim()

    // 检查分类是否存在
    const existingCategory = await db
      .select()
      .from(categories)
      .where(eq(categories.id, categoryId))
      .limit(1)

    if (existingCategory.length === 0) {
      return NextResponse.json(
        { error: '分类不存在' },
        { status: 404 }
      )
    }

    // 检查新名称是否与其他分类冲突
    const duplicateCategory = await db
      .select()
      .from(categories)
      .where(eq(categories.name, categoryName))
      .limit(1)

    if (duplicateCategory.length > 0 && duplicateCategory[0].id !== categoryId) {
      return NextResponse.json(
        { error: '分类名称已存在' },
        { status: 409 }
      )
    }

    // 更新分类
    const [updatedCategory] = await db
      .update(categories)
      .set({
        name: categoryName,
        updatedAt: new Date(),
      })
      .where(eq(categories.id, categoryId))
      .returning()

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: '分类更新成功',
    })

  } catch (error) {
    console.error('更新分类失败:', error)
    return NextResponse.json(
      { error: '更新分类失败' },
      { status: 500 }
    )
  }
}

/**
 * 删除分类
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id } = await params
    const categoryId = parseInt(id)

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: '无效的分类 ID' },
        { status: 400 }
      )
    }

    // 检查分类是否存在
    const existingCategory = await db
      .select()
      .from(categories)
      .where(eq(categories.id, categoryId))
      .limit(1)

    if (existingCategory.length === 0) {
      return NextResponse.json(
        { error: '分类不存在' },
        { status: 404 }
      )
    }

    // 检查是否为"未分类"（不能删除）
    if (existingCategory[0].name === '未分类') {
      return NextResponse.json(
        { error: '不能删除"未分类"分类' },
        { status: 400 }
      )
    }

    // 获取"未分类"的 ID
    const [defaultCategory] = await db
      .select()
      .from(categories)
      .where(eq(categories.name, '未分类'))
      .limit(1)

    if (!defaultCategory) {
      return NextResponse.json(
        { error: '系统错误：找不到默认分类' },
        { status: 500 }
      )
    }

    // 获取该分类下的 prompt 数量
    const [promptCount] = await db
      .select({ count: count() })
      .from(prompts)
      .where(eq(prompts.categoryId, categoryId))

    // 将该分类下的所有 prompts 移动到"未分类"
    if (promptCount.count > 0) {
      await db
        .update(prompts)
        .set({
          categoryId: defaultCategory.id,
          updatedAt: new Date(),
        })
        .where(eq(prompts.categoryId, categoryId))
    }

    // 删除分类
    await db
      .delete(categories)
      .where(eq(categories.id, categoryId))

    return NextResponse.json({
      success: true,
      message: `分类删除成功，${promptCount.count} 个 prompts 已移动到"未分类"`,
      affectedPrompts: promptCount.count,
    })

  } catch (error) {
    console.error('删除分类失败:', error)
    return NextResponse.json(
      { error: '删除分类失败' },
      { status: 500 }
    )
  }
}
