import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { checkAdminPermission } from '@/lib/admin-auth'
import {
  getPublicPromptsForAdmin,
  updatePromptPrivacy,
  updatePromptCategory,
  updatePromptRecommended,
  batchUpdatePromptPrivacy,
  batchUpdatePromptCategory,
  batchUpdatePromptRecommended,
  getCategoryStats,
  getAllCategories,
  isCategoryValid,
  PROMPT_CATEGORIES,
  type AdminPromptFilters,
  type PaginationParams
} from '@/lib/admin-prompts'

/**
 * 获取公开 prompts 列表（管理员视图）
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const isAdmin = await checkAdminPermission(session.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足，需要管理员权限' },
        { status: 403 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || undefined
    const category = searchParams.get('category') || undefined
    const sortBy = searchParams.get('sortBy') as 'createdAt' | 'updatedAt' | 'name' || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc'

    const pagination: PaginationParams = { page, limit }
    const filters: AdminPromptFilters = { search, category, sortBy, sortOrder }

    // 获取数据
    const result = await getPublicPromptsForAdmin(pagination, filters)

    // 获取所有分类和分类统计
    const [allCategories, categoryStats] = await Promise.all([
      getAllCategories(),
      getCategoryStats()
    ])

    return NextResponse.json({
      success: true,
      data: result,
      categories: allCategories,
      categoryStats,
    })
  } catch (error) {
    console.error('获取管理后台 prompts 列表失败:', error)
    return NextResponse.json(
      { error: '获取数据失败' },
      { status: 500 }
    )
  }
}

/**
 * 更新 prompt 设置（隐私、分类等）
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const isAdmin = await checkAdminPermission(session.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足，需要管理员权限' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { action, promptId, promptIds, isPublic, category, isRecommended } = body

    switch (action) {
      case 'updatePrivacy':
        if (typeof promptId !== 'number' || typeof isPublic !== 'boolean') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }
        await updatePromptPrivacy(promptId, isPublic)
        return NextResponse.json({
          success: true,
          message: `已${isPublic ? '公开' : '隐藏'} prompt`
        })

      case 'updateCategory':
        if (typeof promptId !== 'number' || typeof category !== 'string') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }

        // 验证分类是否有效
        const isValidCategory = await isCategoryValid(category)
        if (!isValidCategory) {
          return NextResponse.json(
            { error: '无效的分类' },
            { status: 400 }
          )
        }

        await updatePromptCategory(promptId, category)
        return NextResponse.json({
          success: true,
          message: `已更新分类为：${category}`
        })

      case 'updateRecommended':
        if (typeof promptId !== 'number' || typeof isRecommended !== 'boolean') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }
        await updatePromptRecommended(promptId, isRecommended)
        return NextResponse.json({
          success: true,
          message: `已${isRecommended ? '设为推荐' : '取消推荐'} prompt`
        })

      case 'batchUpdatePrivacy':
        if (!Array.isArray(promptIds) || typeof isPublic !== 'boolean') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }
        await batchUpdatePromptPrivacy(promptIds, isPublic)
        return NextResponse.json({
          success: true,
          message: `已批量${isPublic ? '公开' : '隐藏'} ${promptIds.length} 个 prompt`
        })

      case 'batchUpdateCategory':
        if (!Array.isArray(promptIds) || typeof category !== 'string') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }

        // 验证分类是否有效
        const isValidBatchCategory = await isCategoryValid(category)
        if (!isValidBatchCategory) {
          return NextResponse.json(
            { error: '无效的分类' },
            { status: 400 }
          )
        }

        await batchUpdatePromptCategory(promptIds, category)
        return NextResponse.json({
          success: true,
          message: `已批量更新 ${promptIds.length} 个 prompt 的分类为：${category}`
        })

      case 'batchUpdateRecommended':
        if (!Array.isArray(promptIds) || typeof isRecommended !== 'boolean') {
          return NextResponse.json(
            { error: '参数无效' },
            { status: 400 }
          )
        }
        await batchUpdatePromptRecommended(promptIds, isRecommended)
        return NextResponse.json({
          success: true,
          message: `已批量${isRecommended ? '设为推荐' : '取消推荐'} ${promptIds.length} 个 prompt`
        })

      default:
        return NextResponse.json(
          { error: '不支持的操作' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('更新 prompt 设置失败:', error)
    return NextResponse.json(
      { error: '操作失败' },
      { status: 500 }
    )
  }
}
