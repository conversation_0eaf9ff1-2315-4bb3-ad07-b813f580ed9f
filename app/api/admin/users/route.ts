import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { db } from '@/db'
import { user } from '@/db/auth-schema'
import { session } from '@/db/auth-schema'
import { eq, ilike, and, or, desc, count } from 'drizzle-orm'
import { checkAdminPermission } from '@/lib/admin-auth'
import type { UserListParams, UserListResponse, AdminUser } from '@/types/admin'

/**
 * 获取用户列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url)
    const params: UserListParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      search: searchParams.get('search') || '',
      status: (searchParams.get('status') as any) || 'all',
      role: (searchParams.get('role') as any) || 'all'
    }

    // 构建查询条件
    const conditions = []
    
    // 搜索条件（邮箱）
    if (params.search) {
      conditions.push(
        or(
          ilike(user.email, `%${params.search}%`),
          ilike(user.name, `%${params.search}%`)
        )
      )
    }

    // 角色筛选
    if (params.role === 'admin') {
      conditions.push(eq(user.isAdmin, true))
    } else if (params.role === 'user') {
      conditions.push(eq(user.isAdmin, false))
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(user)
      .where(whereClause)

    const total = totalResult[0]?.count || 0

    // 获取用户列表
    const offset = (params.page! - 1) * params.limit!
    const users = await db
      .select({
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        emailVerified: user.emailVerified,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
      .from(user)
      .where(whereClause)
      .orderBy(desc(user.createdAt))
      .limit(params.limit!)
      .offset(offset)

    // 获取最后登录时间（从 session 表）
    const userIds = users.map(u => u.id)
    let lastLoginsMap: Record<string, Date> = {}

    if (userIds.length > 0) {
      // 为每个用户获取最后登录时间
      const lastLogins = await Promise.all(
        userIds.map(async (userId) => {
          const lastSession = await db.query.session.findFirst({
            where: eq(session.userId, userId),
            orderBy: [desc(session.createdAt)],
            columns: {
              userId: true,
              createdAt: true,
            },
          })
          return lastSession
        })
      )

      lastLogins.forEach(login => {
        if (login) {
          lastLoginsMap[login.userId] = login.createdAt
        }
      })
    }

    // 组装返回数据
    const adminUsers: AdminUser[] = users.map(u => ({
      ...u,
      status: 'active' as const, // 暂时都设为 active，后续可以添加状态字段
      lastLoginAt: lastLoginsMap[u.id] || null
    }))

    const response: UserListResponse = {
      users: adminUsers,
      total,
      page: params.page!,
      limit: params.limit!,
      totalPages: Math.ceil(total / params.limit!)
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('获取用户列表失败:', error)
    return NextResponse.json(
      { error: '获取用户列表失败' },
      { status: 500 }
    )
  }
}
