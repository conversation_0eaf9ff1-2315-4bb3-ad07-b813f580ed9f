import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { db } from '@/db'
import { user } from '@/db/auth-schema'
import { session } from '@/db/auth-schema'
import { eq, desc } from 'drizzle-orm'
import { checkAdminPermission } from '@/lib/admin-auth'
import type { UserUpdateData, AdminUser } from '@/types/admin'

/**
 * 获取单个用户详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id: userId } = await params

    // 获取用户信息
    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        image: true,
        emailVerified: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (!userData) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取最后登录时间
    const lastSession = await db.query.session.findFirst({
      where: eq(session.userId, userId),
      orderBy: [desc(session.createdAt)],
      columns: {
        createdAt: true,
      },
    })

    const adminUser: AdminUser = {
      ...userData,
      status: 'active', // 暂时都设为 active
      lastLoginAt: lastSession?.createdAt || null
    }

    return NextResponse.json(adminUser)

  } catch (error) {
    console.error('获取用户详情失败:', error)
    return NextResponse.json(
      { error: '获取用户详情失败' },
      { status: 500 }
    )
  }
}

/**
 * 更新用户信息
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const headersList = await headers()
    const sessionData = await auth.api.getSession({
      headers: headersList
    })

    if (!sessionData) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const isAdmin = await checkAdminPermission(sessionData.user.id)
    if (!isAdmin) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { id: userId } = await params
    const updateData: UserUpdateData = await request.json()

    // 验证用户是否存在
    const existingUser = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: { id: true },
    })

    if (!existingUser) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 防止管理员取消自己的管理员权限
    if (updateData.isAdmin === false && userId === sessionData.user.id) {
      return NextResponse.json(
        { error: '不能取消自己的管理员权限' },
        { status: 400 }
      )
    }

    // 构建更新数据
    const updateFields: any = {
      updatedAt: new Date()
    }

    if (updateData.name !== undefined) {
      updateFields.name = updateData.name
    }
    if (updateData.email !== undefined) {
      updateFields.email = updateData.email
    }
    if (updateData.isAdmin !== undefined) {
      updateFields.isAdmin = updateData.isAdmin
    }

    // 更新用户
    await db
      .update(user)
      .set(updateFields)
      .where(eq(user.id, userId))

    // 返回更新后的用户信息
    const updatedUser = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        image: true,
        emailVerified: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    const adminUser: AdminUser = {
      ...updatedUser!,
      status: 'active',
      lastLoginAt: null
    }

    return NextResponse.json(adminUser)

  } catch (error) {
    console.error('更新用户失败:', error)
    return NextResponse.json(
      { error: '更新用户失败' },
      { status: 500 }
    )
  }
}
