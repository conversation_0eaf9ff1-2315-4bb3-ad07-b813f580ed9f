import { NextRequest } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { checkAdminPermission } from '@/lib/admin-auth';
import { getAllUsersUsageToday, getUsageOverview, resetAllDailyUsage } from '@/lib/chat-usage';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return Response.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查管理员权限
    const isAdmin = await checkAdminPermission(session.user.id);
    if (!isAdmin) {
      return Response.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'overview') {
      // 获取使用概览
      const overview = await getUsageOverview();
      return Response.json({
        success: true,
        data: overview
      });
    } else {
      // 获取所有用户今日使用情况
      const usageData = await getAllUsersUsageToday();
      return Response.json({
        success: true,
        data: usageData
      });
    }

  } catch (error) {
    console.error('获取聊天使用统计失败:', error);
    return Response.json(
      {
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证管理员身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return Response.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查管理员权限
    const isAdmin = await checkAdminPermission(session.user.id);
    if (!isAdmin) {
      return Response.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, date } = body;

    if (action === 'reset') {
      // 重置指定日期的使用记录
      const resetCount = await resetAllDailyUsage(date);
      return Response.json({
        success: true,
        message: `已重置 ${resetCount} 条使用记录`,
        resetCount
      });
    }

    return Response.json(
      { error: '不支持的操作' },
      { status: 400 }
    );

  } catch (error) {
    console.error('管理聊天使用记录失败:', error);
    return Response.json(
      {
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
