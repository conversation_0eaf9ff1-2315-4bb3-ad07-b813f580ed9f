import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import {
  getUserApiKey,
  generateUserApiKey,
  revokeUserApiKey,
  generateMcpConnectionUrl,
  generateClaudeDesktopConfig,
  maskApiKey,
} from '@/lib/mcp-auth';

/**
 * 获取用户的 MCP API Key 信息
 */
export async function GET() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取用户的 API Key
    const apiKey = await getUserApiKey(session.user.id);
    
    if (!apiKey) {
      return NextResponse.json({
        hasApiKey: false,
        apiKey: null,
        maskedApiKey: null,
        connectionUrl: null,
        claudeConfig: null,
      });
    }

    // 生成连接信息
    const baseUrl = process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const connectionUrl = generateMcpConnectionUrl(baseUrl, apiKey);
    const claudeConfig = generateClaudeDesktopConfig(baseUrl, apiKey);

    return NextResponse.json({
      hasApiKey: true,
      apiKey: apiKey, // 完整的 API Key（仅在需要时返回）
      maskedApiKey: maskApiKey(apiKey),
      connectionUrl,
      claudeConfig,
    });
  } catch (error) {
    console.error('Error getting MCP API key:', error);
    return NextResponse.json(
      { error: '获取 API Key 失败' },
      { status: 500 }
    );
  }
}

/**
 * 生成新的 MCP API Key
 */
export async function POST() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 生成新的 API Key
    const newApiKey = await generateUserApiKey(session.user.id);
    
    // 生成连接信息
    const baseUrl = process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const connectionUrl = generateMcpConnectionUrl(baseUrl, newApiKey);
    const claudeConfig = generateClaudeDesktopConfig(baseUrl, newApiKey);

    return NextResponse.json({
      success: true,
      message: 'API Key 生成成功',
      apiKey: newApiKey,
      maskedApiKey: maskApiKey(newApiKey),
      connectionUrl,
      claudeConfig,
    });
  } catch (error) {
    console.error('Error generating MCP API key:', error);
    return NextResponse.json(
      { error: '生成 API Key 失败' },
      { status: 500 }
    );
  }
}

/**
 * 撤销用户的 MCP API Key
 */
export async function DELETE() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 撤销 API Key
    await revokeUserApiKey(session.user.id);

    return NextResponse.json({
      success: true,
      message: 'API Key 已成功撤销',
    });
  } catch (error) {
    console.error('Error revoking MCP API key:', error);
    return NextResponse.json(
      { error: '撤销 API Key 失败' },
      { status: 500 }
    );
  }
}
