import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { getPromptByIdWithPermission } from '@/lib/prompts';
import { auth } from '@/lib/auth';

// GET /api/chat/prompt/[id] - 获取 prompt 用于聊天（支持权限验证）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const promptId = parseInt(id, 10);

    if (isNaN(promptId)) {
      return NextResponse.json(
        { error: '无效的 Prompt ID' },
        { status: 400 }
      );
    }

    // 获取 prompt（支持权限验证：用户自己的或公开的）
    const prompt = await getPromptByIdWithPermission(promptId, session.user.id);
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt 不存在或已被删除' },
        { status: 404 }
      );
    }

    return NextResponse.json(prompt);
  } catch (error) {
    console.error('Error fetching prompt for chat:', error);
    return NextResponse.json(
      { error: '获取 Prompt 失败' },
      { status: 500 }
    );
  }
}
