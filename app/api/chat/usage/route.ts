import { NextRequest } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { checkChatLimit, getUserUsageHistory } from '@/lib/chat-usage';

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return Response.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '7', 10);

    // 获取当前限额状态
    const limitStatus = await checkChatLimit(session.user.id);

    // 获取历史使用记录
    const history = await getUserUsageHistory(session.user.id, days);

    return Response.json({
      success: true,
      data: {
        current: limitStatus,
        history: history.map(record => ({
          date: record.date,
          count: record.count,
          createdAt: record.createdAt.toISOString(),
        })),
      }
    });

  } catch (error) {
    console.error('获取聊天使用状态失败:', error);
    return Response.json(
      {
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
