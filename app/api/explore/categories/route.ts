import { NextResponse } from 'next/server';
import { getCategoriesWithPublicPromptCount } from '@/lib/explore';

// GET /api/explore/categories - 获取分类列表及其公开 prompt 数量
export async function GET() {
  try {
    const categories = await getCategoriesWithPublicPromptCount();
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: '获取分类列表失败' },
      { status: 500 }
    );
  }
}
