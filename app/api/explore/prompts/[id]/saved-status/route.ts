import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { db } from '@/db';
import { prompts } from '@/db/schema';
import { and, eq } from 'drizzle-orm';

// GET /api/explore/prompts/[id]/saved-status - 检查当前用户是否已保存该 prompt
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const promptId = parseInt(id, 10);

    if (isNaN(promptId)) {
      return NextResponse.json(
        { error: '无效的 Prompt ID' },
        { status: 400 }
      );
    }

    // 检查用户是否已经保存过这个 prompt
    const savedPrompt = await db.query.prompts.findFirst({
      where: and(
        eq(prompts.userId, session.user.id),
        eq(prompts.saveFrom, promptId)
      ),
      columns: {
        id: true,
      },
    });

    return NextResponse.json({
      isSaved: !!savedPrompt,
      savedPromptId: savedPrompt?.id || null,
    });
  } catch (error) {
    console.error('Error checking saved status:', error);
    return NextResponse.json(
      { error: '检查保存状态失败' },
      { status: 500 }
    );
  }
}
