import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { db } from '@/db';
import { prompts, parameters } from '@/db/schema';
import { and, eq } from 'drizzle-orm';
import { getPublicPromptById } from '@/lib/explore';

// POST /api/explore/prompts/[id]/save - 保存 prompt 到用户账号
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const promptId = parseInt(id, 10);

    if (isNaN(promptId)) {
      return NextResponse.json(
        { error: '无效的 Prompt ID' },
        { status: 400 }
      );
    }

    // 检查用户是否已经保存过这个 prompt
    const existingSavedPrompt = await db.query.prompts.findFirst({
      where: and(
        eq(prompts.userId, session.user.id),
        eq(prompts.saveFrom, promptId)
      ),
    });

    if (existingSavedPrompt) {
      return NextResponse.json(
        { error: '您已经保存过这个 Prompt' },
        { status: 409 }
      );
    }

    // 获取原始 prompt 的完整信息
    const originalPrompt = await getPublicPromptById(promptId);
    if (!originalPrompt) {
      return NextResponse.json(
        { error: 'Prompt 不存在或未公开' },
        { status: 404 }
      );
    }

    // 检查用户是否试图保存自己的 prompt
    if (originalPrompt.user.id === session.user.id) {
      return NextResponse.json(
        { error: '不能保存自己的 Prompt' },
        { status: 400 }
      );
    }

    // 生成唯一的 prompt 名称（避免重复）
    let savedPromptName = originalPrompt.name;
    let counter = 1;
    
    // 检查名称是否已存在，如果存在则添加数字后缀
    while (true) {
      const existingPrompt = await db.query.prompts.findFirst({
        where: and(
          eq(prompts.userId, session.user.id),
          eq(prompts.name, savedPromptName)
        ),
      });
      
      if (!existingPrompt) {
        break;
      }
      
      counter++;
      savedPromptName = `${originalPrompt.name} (${counter})`;
    }

    // 创建保存的 prompt
    const [savedPrompt] = await db
      .insert(prompts)
      .values({
        name: savedPromptName,
        description: originalPrompt.description,
        content: originalPrompt.content,
        userId: session.user.id,
        isPublic: false, // 保存的 prompt 默认为私有
        categoryId: originalPrompt.categoryId,
        saveFrom: promptId,
      })
      .returning();

    // 复制原始 prompt 的参数
    if (originalPrompt.parameters && originalPrompt.parameters.length > 0) {
      await db.insert(parameters).values(
        originalPrompt.parameters.map((param) => ({
          promptId: savedPrompt.id,
          name: param.name,
          description: param.description,
          required: param.required,
          userId: session.user.id,
        }))
      );
    }

    return NextResponse.json({
      success: true,
      savedPromptId: savedPrompt.id,
      message: 'Prompt 保存成功',
    });
  } catch (error) {
    console.error('Error saving prompt:', error);
    return NextResponse.json(
      { error: '保存 Prompt 失败' },
      { status: 500 }
    );
  }
}
