import { NextRequest, NextResponse } from 'next/server';
import { getPublicPromptById } from '@/lib/explore';

// GET /api/explore/prompts/[id] - 获取单个公开 prompt
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const promptId = parseInt(id, 10);

    if (isNaN(promptId)) {
      return NextResponse.json(
        { error: '无效的 Prompt ID' },
        { status: 400 }
      );
    }

    const prompt = await getPublicPromptById(promptId);
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt 不存在或未公开' },
        { status: 404 }
      );
    }

    return NextResponse.json(prompt);
  } catch (error) {
    console.error('Error fetching public prompt:', error);
    return NextResponse.json(
      { error: '获取 Prompt 详情失败' },
      { status: 500 }
    );
  }
}
