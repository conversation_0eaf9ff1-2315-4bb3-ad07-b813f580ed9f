import { NextRequest, NextResponse } from 'next/server';
import { getPublicPrompts } from '@/lib/explore';

// GET /api/explore/prompts - 获取公开的 prompts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取分页参数
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    
    // 获取过滤参数
    const search = searchParams.get('search') || undefined;
    const categoryId = searchParams.get('categoryId') 
      ? parseInt(searchParams.get('categoryId')!, 10) 
      : undefined;
    const sortBy = (searchParams.get('sortBy') as 'createdAt' | 'updatedAt' | 'name') || 'createdAt';
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

    // 验证参数
    if (page < 1 || limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: '无效的分页参数' },
        { status: 400 }
      );
    }

    if (categoryId && (isNaN(categoryId) || categoryId < 1)) {
      return NextResponse.json(
        { error: '无效的分类 ID' },
        { status: 400 }
      );
    }

    // 获取数据
    const result = await getPublicPrompts(
      { page, limit },
      { search, categoryId, sortBy, sortOrder }
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching public prompts:', error);
    return NextResponse.json(
      { error: '获取 Prompt 列表失败' },
      { status: 500 }
    );
  }
}
