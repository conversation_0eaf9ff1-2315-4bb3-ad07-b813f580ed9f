import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'

interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

/**
 * 修改用户密码
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { currentPassword, newPassword }: ChangePasswordRequest = await request.json()

    // 验证输入
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: '当前密码和新密码不能为空' },
        { status: 400 }
      )
    }

    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: '新密码长度至少为8位' },
        { status: 400 }
      )
    }

    // 使用 better-auth 的 changePassword API
    try {
      const result = await auth.api.changePassword({
        body: {
          currentPassword,
          newPassword,
        },
        headers: headersList
      })

      return NextResponse.json({
        success: true,
        message: '密码修改成功'
      })

    } catch (authError) {
      console.error('Change password auth error:', authError)
      return NextResponse.json(
        { error: '当前密码错误' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('修改密码失败:', error)
    return NextResponse.json(
      { error: '修改密码失败，请重试' },
      { status: 500 }
    )
  }
}
