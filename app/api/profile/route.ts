import { NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { getUserProfile } from '@/lib/profile'

/**
 * 获取当前用户的个人资料信息
 */
export async function GET() {
  try {
    // 验证用户身份
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 获取用户完整的个人资料信息
    const profile = await getUserProfile(session.user.id)

    if (!profile) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(profile)

  } catch (error) {
    console.error('获取个人资料失败:', error)
    return NextResponse.json(
      { error: '获取个人资料失败' },
      { status: 500 }
    )
  }
}
