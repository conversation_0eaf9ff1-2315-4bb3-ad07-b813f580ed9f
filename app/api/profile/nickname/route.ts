import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { auth } from '@/lib/auth'
import { updateUserNickname } from '@/lib/profile'

/**
 * 更新用户昵称
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers()
    const session = await auth.api.getSession({
      headers: headersList
    })

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const { nickname } = body

    if (!nickname || typeof nickname !== 'string') {
      return NextResponse.json(
        { error: '昵称不能为空' },
        { status: 400 }
      )
    }

    // 更新昵称
    const result = await updateUserNickname(session.user.id, nickname)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: '昵称更新成功' },
      { status: 200 }
    )

  } catch (error) {
    console.error('更新昵称失败:', error)
    return NextResponse.json(
      { error: '更新昵称失败' },
      { status: 500 }
    )
  }
}
