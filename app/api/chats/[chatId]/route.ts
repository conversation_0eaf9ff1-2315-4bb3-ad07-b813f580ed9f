import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { getChatWithMessages, deleteChat, updateChatTitle } from '@/lib/chat';

/**
 * 从 parts JSON 中提取文本内容的辅助函数
 */
function extractTextFromParts(parts: unknown): string | undefined {
  if (!parts) return undefined;

  try {
    // 如果 parts 是字符串，直接返回
    if (typeof parts === 'string') {
      return parts;
    }

    // 如果 parts 是数组，提取文本内容
    if (Array.isArray(parts)) {
      const textParts = parts
        .filter((part: any) => part?.type === 'text' && part?.text)
        .map((part: any) => part.text);
      return textParts.length > 0 ? textParts.join(' ') : undefined;
    }

    // 如果 parts 是对象，尝试提取 text 字段
    if (typeof parts === 'object' && parts !== null) {
      const partsObj = parts as any;
      if (partsObj.text) {
        return partsObj.text;
      }
      if (partsObj.content) {
        return partsObj.content;
      }
    }

    return undefined;
  } catch (error) {
    console.warn('Failed to extract text from parts:', error);
    return undefined;
  }
}

/**
 * 获取单个对话详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 获取对话详情
    const chat = await getChatWithMessages(chatId, session.user.id);

    if (!chat) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt.toISOString(),
        updatedAt: chat.updatedAt.toISOString(),
        messages: chat.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: extractTextFromParts(msg.parts) || '',
          createdAt: msg.createdAt.toISOString(),
        })),
      },
    });

  } catch (error) {
    console.error('获取对话详情错误:', error);
    return NextResponse.json(
      { error: '获取对话详情失败' },
      { status: 500 }
    );
  }
}

/**
 * 更新对话标题
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { title } = body;

    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      return NextResponse.json(
        { error: '对话标题不能为空' },
        { status: 400 }
      );
    }

    // 更新对话标题
    const success = await updateChatTitle(chatId, session.user.id, title.trim());

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: '对话标题更新成功',
    });

  } catch (error) {
    console.error('更新对话标题错误:', error);
    return NextResponse.json(
      { error: '更新对话标题失败' },
      { status: 500 }
    );
  }
}

/**
 * 删除对话
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 删除对话
    const success = await deleteChat(chatId, session.user.id);

    if (!success) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: '对话删除成功',
    });

  } catch (error) {
    console.error('删除对话错误:', error);
    return NextResponse.json(
      { error: '删除对话失败' },
      { status: 500 }
    );
  }
}
