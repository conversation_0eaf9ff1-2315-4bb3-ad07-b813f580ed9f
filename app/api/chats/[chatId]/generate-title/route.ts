import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { getChatWithMessages, updateChatTitle, generateSmartChatTitle } from '@/lib/chat';

/**
 * 从 parts JSON 中提取文本内容的辅助函数
 */
function extractTextFromParts(parts: unknown): string | undefined {
  if (!parts) return undefined;

  try {
    // 如果 parts 是字符串，直接返回
    if (typeof parts === 'string') {
      return parts;
    }

    // 如果 parts 是数组，提取文本内容
    if (Array.isArray(parts)) {
      const textParts = parts
        .filter((part: any) => part?.type === 'text' && part?.text)
        .map((part: any) => part.text);
      return textParts.length > 0 ? textParts.join(' ') : undefined;
    }

    // 如果 parts 是对象，尝试提取 text 字段
    if (typeof parts === 'object' && parts !== null) {
      const partsObj = parts as any;
      if (partsObj.text) {
        return partsObj.text;
      }
      if (partsObj.content) {
        return partsObj.content;
      }
    }

    return undefined;
  } catch (error) {
    console.warn('Failed to extract text from parts:', error);
    return undefined;
  }
}

/**
 * 智能生成对话标题
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = await params;

    if (!chatId || typeof chatId !== 'string') {
      return NextResponse.json(
        { error: '无效的对话 ID' },
        { status: 400 }
      );
    }

    // 获取对话详情和消息
    const chat = await getChatWithMessages(chatId, session.user.id);

    if (!chat) {
      return NextResponse.json(
        { error: '对话不存在或无权访问' },
        { status: 404 }
      );
    }

    // 检查是否有足够的消息来生成标题
    if (!chat.messages || chat.messages.length < 2) {
      return NextResponse.json(
        { error: '对话消息不足，无法生成智能标题' },
        { status: 400 }
      );
    }

    // 获取第一条用户消息和第一条助手回复
    const userMessage = chat.messages.find(msg => msg.role === 'user');
    const assistantMessage = chat.messages.find(msg => msg.role === 'assistant');

    if (!userMessage || !assistantMessage) {
      return NextResponse.json(
        { error: '缺少必要的对话内容' },
        { status: 400 }
      );
    }

    // 从 parts 中提取文本内容
    const userContent = extractTextFromParts(userMessage.parts);
    const assistantContent = extractTextFromParts(assistantMessage.parts);

    if (!userContent || !assistantContent) {
      return NextResponse.json(
        { error: '无法提取对话内容' },
        { status: 400 }
      );
    }

    // 生成智能标题
    const smartTitle = await generateSmartChatTitle(
      userContent,
      assistantContent
    );

    // 更新对话标题
    const success = await updateChatTitle(chatId, session.user.id, smartTitle);

    if (!success) {
      return NextResponse.json(
        { error: '更新对话标题失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      title: smartTitle,
      message: '智能标题生成成功',
    });

  } catch (error) {
    console.error('智能标题生成错误:', error);
    return NextResponse.json(
      { error: '智能标题生成失败' },
      { status: 500 }
    );
  }
}
