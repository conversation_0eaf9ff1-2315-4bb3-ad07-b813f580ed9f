import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { getUserChats, createChat } from '@/lib/chat';
import type { CreateChatRequest } from '@/types/chat';

/**
 * 获取用户的所有对话列表
 */
export async function GET() {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取用户对话列表
    const chats = await getUserChats(session.user.id);

    return NextResponse.json({
      chats,
      total: chats.length,
    });

  } catch (error) {
    console.error('获取对话列表错误:', error);
    return NextResponse.json(
      { error: '获取对话列表失败' },
      { status: 500 }
    );
  }
}

/**
 * 创建新对话
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body: CreateChatRequest = await request.json();
    const { title } = body;

    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      return NextResponse.json(
        { error: '对话标题不能为空' },
        { status: 400 }
      );
    }

    // 创建新对话
    const chat = await createChat(session.user.id, title.trim());

    return NextResponse.json({
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt.toISOString(),
        updatedAt: chat.updatedAt.toISOString(),
        messageCount: 0,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('创建对话错误:', error);
    return NextResponse.json(
      { error: '创建对话失败' },
      { status: 500 }
    );
  }
}
