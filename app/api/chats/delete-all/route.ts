import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { auth } from '@/lib/auth';
import { deleteAllUserChats } from '@/lib/chat';

/**
 * 删除用户的所有对话
 */
export async function DELETE(request: NextRequest) {
  try {
    // 验证用户身份
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList
    });

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 删除用户的所有对话
    const deletedCount = await deleteAllUserChats(session.user.id);

    return NextResponse.json({
      message: '所有对话删除成功',
      deletedCount,
    });

  } catch (error) {
    console.error('删除所有对话错误:', error);
    return NextResponse.json(
      { error: '删除所有对话失败' },
      { status: 500 }
    );
  }
}
