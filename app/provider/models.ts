export interface ModelInfo {
    id: string;
    name: string;
    provider: string;
}

export const modelDetails: Record<string, ModelInfo> = {
    "deepseek-v3": {
        id: "deepseek/deepseek-chat-v3-0324:free",
        name: "Deepseek V3",
        provider: "Deepseek",
    },
    "deepseek-r1": {
        id: "deepseek/deepseek-r1-0528:free",
        name: "Deepseek R1",
        provider: "Deepseek",
    },
    "gpt-4.1-mini": {
        id: "openai/gpt-4.1-mini",
        name: "GPT-4.1 Mini",
        provider: "OpenAI",
    },
    "claude-4-sonnet": {
        id: "anthropic/claude-sonnet-4",
        name: "Claude 4 Sonnet",
        provider: "Anthropic",
    },
};