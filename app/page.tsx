import Link from 'next/link'
import Image from "next/image"
import { Button } from '@/components/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Image
              src="/logo.png"
              alt="Prompt Repo Logo"
              width={32}
              height={32}
              className="h-8 mr-3"
            />
            <span className="text-xl font-bold text-gray-900">Prompt Repo</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
              功能特性
            </Link>
            <Link href="#how-it-works" className="text-gray-600 hover:text-gray-900 transition-colors">
              使用方式
            </Link>
            <Link href="/prompts" className="text-gray-600 hover:text-gray-900 transition-colors">
              开始使用
            </Link>
          </nav>
          <Link href="/prompts">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              立即开始
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-100">
            🎉 完全免费
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            智能化的
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {" "}Prompt 管理平台
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            通过 MCP 协议，让您的自定义 Prompts 在各种 AI 客户端中无缝使用。
            统一管理、便捷编辑、参数化配置，让 AI 交互更加高效。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/prompts">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-3">
                开始管理 Prompts
              </Button>
            </Link>
            <Link href="#how-it-works">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3">
                了解使用方式
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">核心功能特性</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              为 AI 时代设计的 Prompt 管理解决方案
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📝</span>
                </div>
                <CardTitle className="text-xl">完善的管理功能</CardTitle>
                <CardDescription>
                  轻松在一处创建、查看、编辑和删除您的 Prompts
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">🔗</span>
                </div>
                <CardTitle className="text-xl">MCP 协议集成</CardTitle>
                <CardDescription>
                  通过 Model Context Protocol 将您的 Prompts 暴露给 Claude、CherryStudio 等各种 AI 客户端
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <CardTitle className="text-xl">便捷调用</CardTitle>
                <CardDescription>
                  在任何支持 MCP 的 AI 客户端中，一键调用您的自定义 Prompts，提升工作效率
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">🎛️</span>
                </div>
                <CardTitle className="text-xl">参数化配置</CardTitle>
                <CardDescription>
                  支持自定义参数，让您的 Prompts 更加灵活，适应不同的使用场景
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <CardTitle className="text-xl">统一管理</CardTitle>
                <CardDescription>
                  所有 Prompts 集中管理，支持分类、搜索、标签等功能，让组织更有序
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">💰</span>
                </div>
                <CardTitle className="text-xl">完全免费</CardTitle>
                <CardDescription>
                  无需付费，无使用限制，让每个人都能享受高效的 Prompt 管理体验
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section id="how-it-works" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">使用方式</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              三步即可开始使用，让您的 AI 交互更加高效
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">创建 Prompts</h3>
              <p className="text-gray-600">
                在平台上创建和编辑您的自定义 Prompts，支持参数化配置
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">配置 MCP</h3>
              <p className="text-gray-600">
                在您的 AI 客户端中配置 MCP 连接，获取访问您的 Prompts 的能力
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">开始使用</h3>
              <p className="text-gray-600">
                在任何支持 MCP 的 AI 客户端中直接调用您的自定义 Prompts
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            准备好提升您的 AI 交互体验了吗？
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            立即开始使用 PromptRepo，让您的 Prompts 管理变得更加简单高效
          </p>
          <Link href="/prompts">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-3">
              免费开始使用
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Image
                src="/logo.png"
                alt="Prompt Repo Logo"
                width={32}
                height={32}
                className="h-8"
              />
              <span className="text-xl font-bold">PromptRepo</span>
            </div>
            <div className="text-gray-400 text-sm">
              <Link href="/privacy" className='mr-2'>隐私协议</Link>
              © 2025 PromptRepo. 让 AI 交互更加高效.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
