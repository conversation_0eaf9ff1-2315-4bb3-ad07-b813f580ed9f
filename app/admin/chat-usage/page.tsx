import { requireAdminAuth } from '@/lib/admin-auth'
import { AdminLayout } from '@/components/admin/admin-layout'
import { ChatUsageManagement } from '@/components/admin/chat-usage-management'

export const dynamic = 'force-dynamic';

export default async function AdminChatUsagePage() {
  // 验证管理员权限
  await requireAdminAuth('/admin/chat-usage')

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">聊天使用统计</h1>
          <p className="text-muted-foreground mt-2">
            管理用户的AI聊天使用情况和限额设置
          </p>
        </div>
        
        <ChatUsageManagement />
      </div>
    </AdminLayout>
  )
}
