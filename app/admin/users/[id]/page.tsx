import { requireAdminAuth } from '@/lib/admin-auth'
import { AdminLayout } from '@/components/admin/admin-layout'
import { UserDetail } from '@/components/admin/users/user-detail'

interface AdminUserDetailPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function AdminUserDetailPage({ params }: AdminUserDetailPageProps) {
  const { id } = await params
  // 验证管理员权限
  const { user } = await requireAdminAuth(`/admin/users/${id}`)

  return (
    <AdminLayout>
      <UserDetail userId={id} currentUserId={user.id} />
    </AdminLayout>
  )
}
