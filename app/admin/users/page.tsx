import { requireAdminAuth } from '@/lib/admin-auth'
import { AdminLayout } from '@/components/admin/admin-layout'
import { UserList } from '@/components/admin/users/user-list'

export const dynamic = 'force-dynamic';

export default async function AdminUsersPage() {
  // 验证管理员权限
  const { user } = await requireAdminAuth('/admin/users')

  return (
    <AdminLayout
      title="用户管理"
      description="管理系统中的所有用户，包括用户信息、权限设置和状态管理"
    >
      <UserList currentUserId={user.id} />
    </AdminLayout>
  )
}
