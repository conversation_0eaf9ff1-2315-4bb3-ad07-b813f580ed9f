import { requireAdminAuth } from '@/lib/admin-auth'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import {
  FileText,
  Users,
  BarChart3,
  Settings,
  ArrowRight,
  Shield,
  Activity,
  TrendingUp,
  Tag
} from 'lucide-react'
import { getPublicPromptsForAdmin, getCategoryStats } from '@/lib/admin-prompts'

export const dynamic = 'force-dynamic';

export default async function AdminDashboard() {
  // 验证管理员权限
  const { user } = await requireAdminAuth('/admin')

  // 获取基础统计数据
  const promptsData = await getPublicPromptsForAdmin(
    { page: 1, limit: 1 },
    { sortBy: 'createdAt', sortOrder: 'desc' }
  )
  const categoryStats = await getCategoryStats()

  const dashboardCards = [
    {
      title: 'Prompt 管理',
      description: '管理所有公开的 prompts，设置分类和隐私控制',
      icon: FileText,
      href: '/admin/prompts',
      stats: `${promptsData.total} 个公开 prompts`,
      color: 'bg-blue-500'
    },
    {
      title: '分类管理',
      description: '管理 prompt 分类，创建、编辑和删除分类',
      icon: Tag,
      href: '/admin/categories',
      stats: `${Object.keys(categoryStats).length} 个分类`,
      color: 'bg-indigo-500'
    },
    {
      title: '使用统计',
      description: '管理用户AI聊天使用情况和限额设置',
      icon: Activity,
      href: '/admin/user-usage',
      stats: '查看使用详情',
      color: 'bg-purple-500'
    },
    {
      title: '用户管理',
      description: '管理用户账户和权限设置',
      icon: Users,
      href: '/admin/users',
      stats: '功能开发中',
      color: 'bg-green-500'
    },
  ]

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* 欢迎区域 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-white/20 rounded-lg">
              <Shield className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">欢迎回来，{user.name}</h1>
              <p className="text-blue-100 mt-2">
                管理员控制面板 - 管理 PromptRepo 的所有功能
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-blue-100">
            <Activity className="h-4 w-4" />
            <span className="text-sm">系统运行正常</span>
          </div>
        </div>

        {/* 快速统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">公开 Prompts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{promptsData.total}</div>
              <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                <TrendingUp className="h-3 w-3" />
                总计公开的 prompts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">分类数量</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Object.keys(categoryStats).length}
              </div>
              <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                <TrendingUp className="h-3 w-3" />
                已使用的分类
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">管理员权限</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">已激活</div>
              <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                <Activity className="h-3 w-3" />
                完整管理权限
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 管理功能卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {dashboardCards.map((card) => {
            const IconComponent = card.icon;
            return (
              <Link key={card.href} href={card.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`p-3 rounded-lg ${card.color} text-white`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-lg mb-2">{card.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {card.description}
                        </p>
                        <div className="flex items-center gap-2 text-sm">
                          <Badge variant="secondary">{card.stats}</Badge>
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* 分类统计 */}
        {Object.keys(categoryStats).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>分类分布</CardTitle>
              <p className="text-sm text-muted-foreground">
                各分类下的 prompt 数量统计
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(categoryStats)
                  .sort(([,a], [,b]) => b - a)
                  .map(([category, count]) => (
                    <div key={category} className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{count}</div>
                      <div className="text-sm text-gray-600 mt-1">{category}</div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  )
}
