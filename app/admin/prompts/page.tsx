import { requireAdminAuth } from '@/lib/admin-auth'
import { getPublicPromptsForAdmin, getCategoryStats, getAllCategories } from '@/lib/admin-prompts'
import { AdminLayout } from '@/components/admin/admin-layout'
import { PromptList } from '@/components/admin/prompt-list'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FileText, Users, Eye, Tag } from 'lucide-react'

export const dynamic = 'force-dynamic';

export default async function AdminPromptsPage() {
  // 验证管理员权限
  await requireAdminAuth('/admin/prompts')

  // 获取初始数据和分类信息
  const [initialData, categoryStats, allCategories] = await Promise.all([
    getPublicPromptsForAdmin(
      { page: 1, limit: 10 },
      { sortBy: 'createdAt', sortOrder: 'desc' }
    ),
    getCategoryStats(),
    getAllCategories()
  ])

  return (
    <AdminLayout
      title="Prompt 管理"
      description="管理所有公开的 prompts，包括分类设置和隐私控制"
    >
      <div className="space-y-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总计 Prompts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{initialData.total}</div>
              <p className="text-xs text-muted-foreground">
                公开的 prompts 总数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃作者</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(initialData.data.map(p => p.user.id)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                当前页面的作者数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最多分类</CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Object.entries(categoryStats).length > 0 
                  ? Object.entries(categoryStats).reduce((a, b) => 
                      categoryStats[a[0]] > categoryStats[b[0]] ? a : b
                    )[0]
                  : '未分类'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                包含最多 prompts 的分类
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">当前页面</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{initialData.data.length}</div>
              <p className="text-xs text-muted-foreground">
                第 {initialData.page} 页，共 {initialData.totalPages} 页
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 分类统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              分类统计
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {Object.entries(categoryStats).map(([category, count]) => (
                <Badge key={category} variant="secondary" className="text-sm">
                  {category}: {count}
                </Badge>
              ))}
              {Object.keys(categoryStats).length === 0 && (
                <p className="text-gray-500 text-sm">暂无分类数据</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Prompt 列表 */}
        <PromptList initialData={initialData} categories={allCategories} />
      </div>
    </AdminLayout>
  )
}
