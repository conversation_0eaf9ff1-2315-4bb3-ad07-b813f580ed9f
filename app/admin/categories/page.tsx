import { requireAdminAuth } from '@/lib/admin-auth'
import { AdminLayout } from '@/components/admin/admin-layout'
import { CategoryList } from '@/components/admin/categories/category-list'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tag, Plus, FileText } from 'lucide-react'

export const dynamic = 'force-dynamic';

async function getCategoriesData() {
  try {
    // 直接使用数据库查询，避免 API 调用的认证问题
    const { getCategoriesWithStats } = await import('@/lib/categories')
    return await getCategoriesWithStats()
  } catch (error) {
    console.error('获取分类数据失败:', error)
    return []
  }
}

export default async function AdminCategoriesPage() {
  // 验证管理员权限
  await requireAdminAuth('/admin/categories')

  // 获取分类数据
  const categoriesData = await getCategoriesData()
  
  // 计算统计信息
  const totalCategories = categoriesData.length
  const totalPrompts = categoriesData.reduce((sum: number, cat: any) => sum + cat.promptCount, 0)
  const categoriesWithPrompts = categoriesData.filter((cat: any) => cat.promptCount > 0).length

  return (
    <AdminLayout
      title="分类管理"
      description="管理 prompt 分类，包括创建、编辑和删除分类"
    >
      <div className="space-y-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总分类数</CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCategories}</div>
              <p className="text-xs text-muted-foreground">
                包含所有分类
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">有内容分类</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categoriesWithPrompts}</div>
              <p className="text-xs text-muted-foreground">
                包含 prompts 的分类
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总 Prompts</CardTitle>
              <Plus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPrompts}</div>
              <p className="text-xs text-muted-foreground">
                所有分类下的 prompts
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 分类分布 */}
        {categoriesData.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                分类分布
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {categoriesData
                  .sort((a: any, b: any) => b.promptCount - a.promptCount)
                  .map((category: any) => (
                    <Badge 
                      key={category.id} 
                      variant={category.promptCount > 0 ? "default" : "secondary"}
                      className="text-sm"
                    >
                      {category.name}: {category.promptCount}
                    </Badge>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 分类列表 */}
        <CategoryList initialData={categoriesData} />
      </div>
    </AdminLayout>
  )
}
