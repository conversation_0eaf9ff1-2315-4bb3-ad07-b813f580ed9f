"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { authClient } from "@/lib/auth-client"
import { translateOAuthError } from "@/lib/error-messages"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { InlineLoading } from "@/components/ui/loading"

function OAuthCallbackContent() {
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading")
  const [message, setMessage] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // 检查 URL 中是否有错误参数
        const error = searchParams.get('error')
        if (error) {
          setStatus("error")
          setMessage(`登录失败: ${translateOAuthError(error)}`)
          setTimeout(() => {
            router.push("/login")
          }, 3000)
          return
        }

        // 检查用户是否已经登录
        const { data: sessionData } = await authClient.getSession()

        if (sessionData) {
          // 用户已登录，获取重定向路径
          const redirectTo = localStorage.getItem('auth_redirect') || '/prompts'
          localStorage.removeItem('auth_redirect') // 清理存储的重定向路径

          setStatus("success")
          setMessage("登录成功！正在跳转...")

          // 延迟一下再跳转，让用户看到成功消息
          setTimeout(() => {
            router.push(redirectTo)
          }, 1500)
        } else {
          // 用户未登录，可能是 OAuth 流程失败
          setStatus("error")
          setMessage("登录失败，请重试")

          // 3秒后重定向到登录页面
          setTimeout(() => {
            router.push("/login")
          }, 3000)
        }
      } catch (error) {
        console.error("OAuth 回调处理失败:", error)
        setStatus("error")
        setMessage("登录过程中发生错误，请重试")

        // 3秒后重定向到登录页面
        setTimeout(() => {
          router.push("/login")
        }, 3000)
      }
    }

    // 延迟一下再检查，确保 OAuth 流程完成
    const timer = setTimeout(handleOAuthCallback, 1000)

    return () => clearTimeout(timer)
  }, [router, searchParams])

  return (
    <div className="min-h-[calc(100dvh-4rem)] flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>
            {status === "loading" && "处理登录..."}
            {status === "success" && "登录成功"}
            {status === "error" && "登录失败"}
          </CardTitle>
          <CardDescription>
            {status === "loading" && "正在验证您的登录信息，请稍候..."}
            {status === "success" && "欢迎回来！即将跳转到您的目标页面。"}
            {status === "error" && "很抱歉，登录过程中遇到了问题。"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            {status === "loading" && (
              <InlineLoading text="验证中..." size="md" />
            )}
            {status === "success" && (
              <div className="text-green-600">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium">成功</span>
                </div>
                <p className="text-sm">{message}</p>
              </div>
            )}
            {status === "error" && (
              <div className="text-red-600">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span className="font-medium">失败</span>
                </div>
                <p className="text-sm">{message}</p>
                <p className="text-xs text-gray-500 mt-2">即将自动跳转到登录页面...</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function OAuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-[calc(100dvh-4rem)] flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>处理登录...</CardTitle>
            <CardDescription>正在验证您的登录信息，请稍候...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <InlineLoading text="验证中..." size="md" />
            </div>
          </CardContent>
        </Card>
      </div>
    }>
      <OAuthCallbackContent />
    </Suspense>
  )
}
