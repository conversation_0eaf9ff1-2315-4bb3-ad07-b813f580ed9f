import { requireAuth } from '@/lib/auth-server'
import { getUserProfile } from '@/lib/profile'
import { ProfilePage } from '@/components/profile/profile-page'
import type { Metadata } from 'next'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: '个人资料 - Prompt Repo',
  description: '管理您的个人资料和账户设置',
}

export default async function ProfilePageRoute() {
  // 验证用户身份，如果未登录会自动重定向到登录页面
  const session = await requireAuth('/profile')

  // 获取用户完整的个人资料信息
  const profile = await getUserProfile(session.user.id)

  if (!profile) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载个人资料</p>
        </div>
      </div>
    )
  }

  return <ProfilePage profile={profile} />
}