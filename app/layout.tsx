import type { Metada<PERSON> } from "next";
import { Analytics } from "@vercel/analytics/next"
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { QueryProvider } from "@/components/providers/query-provider";

export const metadata: Metadata = {
  title: "Prompt Repo",
  description: "Your Prompt Template Library",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const messages = await getMessages();

  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body>
        <QueryProvider>
          <NextIntlClientProvider messages={messages}>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              forcedTheme="light"
              disableTransitionOnChange
            >
              <Navigation />
              {children}
              <Toaster position="top-center" />
            </ThemeProvider>
          </NextIntlClientProvider>
        </QueryProvider>
        <Analytics />
      </body>
    </html>
  );
}
