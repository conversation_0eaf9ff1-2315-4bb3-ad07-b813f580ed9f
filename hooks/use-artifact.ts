'use client';

import { create } from 'zustand';
import type { ArtifactData, ArtifactState } from '@/types/artifact';

interface ArtifactStore extends ArtifactState {
  openArtifact: (artifact: ArtifactData) => void;
  closeArtifact: () => void;
  setActiveTab: (tab: 'preview' | 'code') => void;
}

export const useArtifactStore = create<ArtifactStore>((set) => ({
  isOpen: false,
  currentArtifact: null,
  activeTab: 'preview',
  
  openArtifact: (artifact: ArtifactData) => {
    set({
      isOpen: true,
      currentArtifact: artifact,
      activeTab: 'preview', // 默认打开预览标签页
    });
  },
  
  closeArtifact: () => {
    set({
      isOpen: false,
      currentArtifact: null,
      activeTab: 'preview',
    });
  },
  
  setActiveTab: (tab: 'preview' | 'code') => {
    set({ activeTab: tab });
  },
}));

// 便捷的 hook 用于组件中使用
export const useArtifact = () => {
  const store = useArtifactStore();
  
  return {
    ...store,
    // 添加一些便捷方法
    isHtmlArtifact: store.currentArtifact?.type === 'html',
    isSvgArtifact: store.currentArtifact?.type === 'svg',
  };
};
