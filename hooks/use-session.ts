"use client"

import { useEffect, useState } from "react"
import { authClient } from "@/lib/auth-client"

export interface User {
  id: string
  name: string
  email: string
  image?: string | null
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Session {
  id: string
  userId: string
  expiresAt: Date
  token: string
  ipAddress?: string | null
  userAgent?: string | null
}

export function useSession() {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const getSession = async () => {
      try {
        const { data } = await authClient.getSession()
        if (data) {
          setUser(data.user)
          setSession(data.session)
        }
      } catch (error) {
        console.error("获取会话失败:", error)
      } finally {
        setIsLoading(false)
      }
    }

    getSession()
  }, [])

  const signOut = async () => {
    try {
      await authClient.signOut()
      setUser(null)
      setSession(null)
    } catch (error) {
      console.error("登出失败:", error)
    }
  }

  return {
    user,
    session,
    isLoading,
    isAuthenticated: !!user,
    signOut,
  }
}
