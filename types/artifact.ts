export interface ArtifactData {
  id: string;
  type: 'html' | 'svg';
  content: string;
  title?: string;
  description?: string;
}

export interface ArtifactState {
  isOpen: boolean;
  currentArtifact: ArtifactData | null;
  activeTab: 'preview' | 'code';
}

export interface ArtifactCardProps {
  artifact: ArtifactData;
  onClick: () => void;
}

export interface ArtifactSidebarProps {
  isOpen: boolean;
  artifact: ArtifactData | null;
  activeTab: 'preview' | 'code';
  onClose: () => void;
  onTabChange: (tab: 'preview' | 'code') => void;
}

export interface ArtifactPreviewProps {
  artifact: ArtifactData;
}

export interface ArtifactCodeProps {
  artifact: ArtifactData;
}
