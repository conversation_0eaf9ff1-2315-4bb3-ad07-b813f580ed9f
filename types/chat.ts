import { Chat, Message } from '@/db/schema';

export interface ChatWithMessages extends Chat {
  messages: Message[];
}

export interface CreateChatRequest {
  title: string;
}

export interface CreateMessageRequest {
  chatId: string;
  content: string;
  role: 'user' | 'assistant';
}

export interface ChatResponse {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  lastMessage?: string;
}

export interface StreamingChatRequest {
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  chatId?: string;
  title?: string;
}

export interface StreamingChatResponse {
  chatId: string;
  messageId?: number;
  content: string;
  done: boolean;
  type?: 'init' | 'content' | 'complete' | 'error';
  error?: string;
}
