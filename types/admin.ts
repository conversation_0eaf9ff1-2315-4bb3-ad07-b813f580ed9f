export interface AdminUser {
  id: string
  name: string
  email: string
  image?: string | null
  emailVerified: boolean
  isAdmin: boolean
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date | null
  status: 'active' | 'inactive'
}

export interface UserListParams {
  page?: number
  limit?: number
  search?: string
  status?: 'all' | 'active' | 'inactive'
  role?: 'all' | 'user' | 'admin'
}

export interface UserListResponse {
  users: AdminUser[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface UserUpdateData {
  name?: string
  email?: string
  isAdmin?: boolean
  status?: 'active' | 'inactive'
}
