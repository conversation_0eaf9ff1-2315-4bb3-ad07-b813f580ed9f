// 重新导出数据库类型
export type { Prompt, NewPrompt, Parameter, NewParameter, Category, NewCategory } from '../db/schema';
import type { Parameter, Category } from '../db/schema';

// 扩展类型，包含关联数据
export type PromptWithParameters = {
  id: number;
  name: string;
  description: string | null;
  content: string;
  isPublic: boolean;
  isRecommended: boolean;
  categoryId: number | null;
  createdAt: Date;
  updatedAt: Date;
  parameters: Parameter[];
  category?: Category | null;
};

// 表单数据类型
export type PromptFormData = {
  name: string;
  description?: string;
  content: string;
  isPublic?: boolean;
  categoryId?: number;
  parameters: ParameterFormData[];
};

export type ParameterFormData = {
  name: string;
  description?: string;
  required: boolean;
};

// 分类相关类型
export type CategoryFormData = {
  name: string;
};

export type CategoryWithStats = Category & {
  promptCount: number;
};