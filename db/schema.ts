import { pgTable, text, boolean, serial, integer, json, timestamp, unique, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { user } from './auth-schema';

// Categories 表
export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  name: text('name').notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// Prompts 表
export const prompts = pgTable('prompts', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  content: text('content').notNull(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  isPublic: boolean('is_public').default(false).notNull(),
  isRecommended: boolean('is_recommended').default(false).notNull(), // 推荐状态字段
  categoryId: integer('category_id').references(() => categories.id, { onDelete: 'set null' }),
  saveFrom: integer('save_from'), // 保存来源字段，引用其他 prompt 的 ID
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => [
  // 确保同一用户下 prompt 名称唯一
  unique().on(table.userId, table.name),
  // 确保同一用户不能重复保存同一个 prompt
  unique().on(table.userId, table.saveFrom),
]);

// Parameters 表
export const parameters = pgTable('parameters', {
  id: serial('id').primaryKey(),
  promptId: integer('prompt_id').notNull().references(() => prompts.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  description: text('description'),
  required: boolean('required').default(false).notNull(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// Chats 表 - 存储对话信息
export const chats = pgTable('chats', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title').notNull(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// Messages 表 - 存储消息记录
export const messages = pgTable('messages', {
  id: serial('id').primaryKey(),
  chatId: uuid('chat_id').notNull().references(() => chats.id, { onDelete: 'cascade' }),
  role: text('role', { enum: ['user', 'assistant'] }).notNull(),
  // content: text('content').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// Chat Usage 表 - 存储用户每日聊天使用记录
export const chatUsage = pgTable('chat_usage', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  date: text('date').notNull(), // 格式: YYYY-MM-DD
  count: integer('count').default(0).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (table) => [
  // 确保每个用户每天只有一条记录
  unique().on(table.userId, table.date),
]);

// 关系定义
export const categoriesRelations = relations(categories, ({ many }) => ({
  prompts: many(prompts),
}));

export const promptsRelations = relations(prompts, ({ many, one }) => ({
  parameters: many(parameters),
  user: one(user, {
    fields: [prompts.userId],
    references: [user.id],
  }),
  category: one(categories, {
    fields: [prompts.categoryId],
    references: [categories.id],
  }),
  originalPrompt: one(prompts, {
    fields: [prompts.saveFrom],
    references: [prompts.id],
    relationName: 'savedFrom',
  }),
  savedCopies: many(prompts, {
    relationName: 'savedFrom',
  }),
}));

export const parametersRelations = relations(parameters, ({ one }) => ({
  prompt: one(prompts, {
    fields: [parameters.promptId],
    references: [prompts.id],
  }),
  user: one(user, {
    fields: [parameters.userId],
    references: [user.id],
  }),
}));

export const chatsRelations = relations(chats, ({ many, one }) => ({
  messages: many(messages),
  user: one(user, {
    fields: [chats.userId],
    references: [user.id],
  }),
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  chat: one(chats, {
    fields: [messages.chatId],
    references: [chats.id],
  }),
  user: one(user, {
    fields: [messages.userId],
    references: [user.id],
  }),
}));

export const chatUsageRelations = relations(chatUsage, ({ one }) => ({
  user: one(user, {
    fields: [chatUsage.userId],
    references: [user.id],
  }),
}));

export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;
export type Prompt = typeof prompts.$inferSelect;
export type NewPrompt = typeof prompts.$inferInsert;
export type Parameter = typeof parameters.$inferSelect;
export type NewParameter = typeof parameters.$inferInsert;
export type Chat = typeof chats.$inferSelect;
export type NewChat = typeof chats.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type DBMessage = typeof messages.$inferInsert;
export type ChatUsage = typeof chatUsage.$inferSelect;
export type NewChatUsage = typeof chatUsage.$inferInsert;