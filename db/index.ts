import 'dotenv/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { drizzle as neon } from 'drizzle-orm/neon-http';
import * as authSchema from './auth-schema';
import * as promptSchema from './schema';

const getDbInstance = () => {
  if (process.env.VERCEL) {
    return neon(process.env.DATABASE_URL!, {
      schema: { ...authSchema, ...promptSchema }
    });
  } else {
    return drizzle(process.env.DATABASE_URL!, {
      schema: { ...authSchema, ...promptSchema }
    });
  }
}

export const db = getDbInstance();