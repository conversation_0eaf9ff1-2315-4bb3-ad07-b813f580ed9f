import { NextRequest, NextResponse } from 'next/server'
import { getSessionCookie } from 'better-auth/cookies'

export async function middleware(request: NextRequest) {
  // 使用 better-auth 检查会话 cookie
  const sessionCookie = getSessionCookie(request)

  // 如果没有有效会话 cookie，重定向到登录页面
  if (!sessionCookie) {
    const loginUrl = new URL('/login', request.url)
    // 保存用户原本想要访问的页面，登录后可以重定向回来
    loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
    return NextResponse.redirect(loginUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // 匹配所有 prompts 相关路由
    '/prompts/:path*',
    // 匹配所有 mcp 相关路由
    '/mcp/:path*',
    // 匹配所有 admin 相关路由
    '/admin/:path*',
    // 匹配所有 chat 相关路由
    '/chat/:path*',
  ]
}
