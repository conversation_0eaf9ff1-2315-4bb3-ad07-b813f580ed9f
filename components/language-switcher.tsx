'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Globe } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

const languages = [
  { code: 'zh', name: '中文'},
  { code: 'en', name: 'English'},
]

function getLanguageFromCookie(): string {
  if (typeof window === 'undefined') return 'en'
  
  return document.cookie
    .split(';')
    .find(cookie => cookie.trim().startsWith('language='))
    ?.split('=')[1] || 'en'
}

export function LanguageSwitcher() {
  // Always start with 'en' to ensure server and client render the same initially
  const [currentLanguage, setCurrentLanguage] = useState('en')
  const [isHydrated, setIsHydrated] = useState(false)

  // Update language from cookie after hydration
  useEffect(() => {
    const cookieLanguage = getLanguageFromCookie()
    setCurrentLanguage(cookieLanguage)
    setIsHydrated(true)
  }, [])

  const handleLanguageChange = (languageCode: string) => {
    // 设置 cookie
    document.cookie = `language=${languageCode}; path=/; max-age=31536000` // 1年过期
    setCurrentLanguage(languageCode)
    
    // 刷新页面以应用新语言
    window.location.reload()
  }

  // Use the actual current language only after hydration, otherwise use default
  const displayLanguage = isHydrated ? currentLanguage : 'en'
  const currentLang = languages.find(lang => lang.code === displayLanguage) || languages[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild  className="hidden sm:flex">
        <Button variant="ghost" size="sm" className="mx-2">
          <Globe className="h-4 w-4" />
          <span className="hidden md:inline">{currentLang.name}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={displayLanguage === language.code ? 'bg-accent' : ''}
          >
            {language.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
