"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { authClient } from "@/lib/auth-client"
import { translateErrorMessage } from "@/lib/error-messages"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ButtonLoading } from "@/components/ui/loading"
import Image from "next/image"

export function LoginForm() {
  const [isGithubLoading, setIsGithubLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [isEmailLoading, setIsEmailLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()
  const message = searchParams.get("message")

  // 邮箱密码登录表单状态
  const [loginForm, setLoginForm] = useState({
    email: "",
    password: ""
  })

  const handleGithubLogin = async () => {
    setIsGithubLoading(true)
    setError("")

    try {
      // 保存重定向参数到 localStorage，因为 OAuth 流程会导航到外部页面
      const redirectTo = searchParams.get('redirect') || '/prompts'
      localStorage.setItem('auth_redirect', redirectTo)

      // 启动 GitHub OAuth 流程
      await authClient.signIn.social({
        provider: "github",
        callbackURL: "/oauth-callback",
      })
    } catch (err) {
      console.error("GitHub 登录失败:", err)
      setError("GitHub 登录失败，请重试")
      setIsGithubLoading(false)
    }
  }

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true)
    setError("")

    try {
      // 保存重定向参数到 localStorage，因为 OAuth 流程会导航到外部页面
      const redirectTo = searchParams.get('redirect') || '/prompts'
      localStorage.setItem('auth_redirect', redirectTo)

      // 启动 Google OAuth 流程
      await authClient.signIn.social({
        provider: "google",
        callbackURL: "/oauth-callback",
      })
    } catch (err) {
      console.error("Google 登录失败:", err)
      setError("Google 登录失败，请重试")
      setIsGoogleLoading(false)
    }
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsEmailLoading(true)
    setError("")
    setSuccess("")

    try {
      const result = await authClient.signIn.email({
        email: loginForm.email,
        password: loginForm.password,
      })

      if (result.error) {
        setError(translateErrorMessage(result.error.message, "登录失败，请检查邮箱和密码"))
      } else {
        setSuccess("登录成功！正在跳转...")
        const redirectTo = searchParams.get('redirect') || '/prompts'
        window.location.href=redirectTo
      }
    } catch (err) {
      console.error("邮箱登录失败:", err)
      setError("登录失败，请检查邮箱和密码")
    } finally {
      setIsEmailLoading(false)
    }
  }

  return (
    <>
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">登录</CardTitle>
        </CardHeader>
        <CardContent className="pb-4">
          {message && (
            <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
              {message}
            </div>
          )}

          {error && (
            <div className="mb-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
              {success}
            </div>
          )}

          <div className="space-y-4">
            <div className="space-y-2">
              <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleGoogleLogin}
              disabled={isGoogleLoading || isGithubLoading || isEmailLoading}
            >
              <Image src="/images/google.svg" alt="Google" width={16} height={16} className="mr-2" />
              {isGoogleLoading ? "连接 Google..." : "使用 Google 登录"}
            </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleGithubLogin}
                disabled={isGithubLoading || isGoogleLoading || isEmailLoading}
              >
                <Image src="/images/github.svg" alt="GitHub" width={16} height={16} className="mr-2" />
                {isGithubLoading ? (
                  <ButtonLoading text="连接 GitHub..." />
                ) : (
                  "使用 GitHub 登录"
                )}
              </Button>
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">或</span>
              </div>
            </div>

            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="login-email">邮箱</Label>
                <Input
                  id="login-email"
                  type="email"
                  placeholder="请输入邮箱"
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="login-password">密码</Label>
                <Input
                  id="login-password"
                  type="password"
                  placeholder="请输入密码"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={isEmailLoading || isGithubLoading || isGoogleLoading}
              >
                {isEmailLoading ? (
                  <ButtonLoading text="登录中..." />
                ) : (
                  "登录"
                )}
              </Button>
            </form>
          </div>
          <div className="mt-6 text-center">
            <span className="text-sm text-gray-600">还没有账户？</span>
            <Link href="/signup" className="text-sm text-blue-600 hover:underline ml-1">
              立即注册
            </Link>
          </div>
        </CardContent>
      </Card>
      <div className="mt-4 text-xs text-gray-500 text-center">
        登录代表同意
        <Link href="/terms" target="_blank" className="text-blue-600 hover:underline mx-1">
          服务条款
        </Link>
        和
        <Link href="/privacy" target="_blank" className="text-blue-600 hover:underline mx-1">
          隐私政策
        </Link>
      </div>
    </>
  )
}
