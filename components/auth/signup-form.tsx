"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { authClient } from "@/lib/auth-client"
import { translateErrorMessage } from "@/lib/error-messages"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ButtonLoading } from "@/components/ui/loading"
import Image from "next/image"

export function SignupForm() {
  const [isGithubLoading, setIsGithubLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [isEmailLoading, setIsEmailLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()
  const message = searchParams.get("message")

  // 注册表单状态
  const [registerForm, setRegisterForm] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: ""
  })

  const handleGithubLogin = async () => {
    setIsGithubLoading(true)
    setError("")

    try {
      // 保存重定向参数到 localStorage，因为 OAuth 流程会导航到外部页面
      const redirectTo = searchParams.get('redirect') || '/prompts'
      localStorage.setItem('auth_redirect', redirectTo)

      // 启动 GitHub OAuth 流程
      await authClient.signIn.social({
        provider: "github",
        callbackURL: "/oauth-callback",
      })
    } catch (err) {
      console.error("GitHub 注册失败:", err)
      setError("GitHub 注册失败，请重试")
      setIsGithubLoading(false)
    }
  }

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true)
    setError("")

    try {
      // 保存重定向参数到 localStorage，因为 OAuth 流程会导航到外部页面
      const redirectTo = searchParams.get('redirect') || '/prompts'
      localStorage.setItem('auth_redirect', redirectTo)

      // 启动 Google OAuth 流程
      await authClient.signIn.social({
        provider: "google",
        callbackURL: "/oauth-callback",
      })
    } catch (err) {
      console.error("Google 注册失败:", err)
      setError("Google 注册失败，请重试")
      setIsGoogleLoading(false)
    }
  }

  const handleEmailRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsEmailLoading(true)
    setError("")
    setSuccess("")

    // 验证密码确认
    if (registerForm.password !== registerForm.confirmPassword) {
      setError("密码确认不匹配")
      setIsEmailLoading(false)
      return
    }

    // 验证密码长度
    if (registerForm.password.length < 8) {
      setError("密码长度至少为8位")
      setIsEmailLoading(false)
      return
    }

    try {
      const result = await authClient.signUp.email({
        email: registerForm.email,
        password: registerForm.password,
        name: registerForm.name,
      })

      if (result.error) {
        setError(translateErrorMessage(result.error.message, "注册失败，请重试"))
      } else {
        setSuccess("注册成功！正在跳转...")
        const redirectTo = searchParams.get('redirect') || '/prompts'
        window.location.href=redirectTo
      }
    } catch (err) {
      console.error("邮箱注册失败:", err)
      setError("注册失败，请重试")
    } finally {
      setIsEmailLoading(false)
    }
  }

  return (
    <>
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">注册账户</CardTitle>
        </CardHeader>
        <CardContent className="pb-4">
          {message && (
            <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
              {message}
            </div>
          )}

          {error && (
            <div className="mb-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-4 text-sm text-green-600 bg-green-50 p-3 rounded-md">
              {success}
            </div>
          )}

          <div className="space-y-2 mt-2">
            <Button
            type="button"
            variant="outline"
            className="w-full"
            onClick={handleGoogleLogin}
            disabled={isGoogleLoading || isGithubLoading || isEmailLoading}
          >
            <Image src="/images/google.svg" alt="Google" width={16} height={16} className="mr-2" />
            {isGoogleLoading ? "连接 Google..." : "使用 Google 注册"}
          </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleGithubLogin}
              disabled={isGithubLoading || isGoogleLoading || isEmailLoading}
            >
              <Image src="/images/github.svg" alt="GitHub" width={16} height={16} className="mr-2" />
              {isGithubLoading ? (
              <ButtonLoading text="连接 GitHub..." />
            ) : (
              "使用 GitHub 注册"
            )}
            </Button>
          </div>
          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">或</span>
            </div>
          </div>
          <form onSubmit={handleEmailRegister} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="register-name">昵称</Label>
              <Input
                id="register-name"
                type="text"
                placeholder="请输入昵称"
                value={registerForm.name}
                onChange={(e) => setRegisterForm({ ...registerForm, name: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-email">邮箱</Label>
              <Input
                id="register-email"
                type="email"
                placeholder="请输入邮箱"
                value={registerForm.email}
                onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-password">密码</Label>
              <Input
                id="register-password"
                type="password"
                placeholder="请输入密码（至少8位）"
                value={registerForm.password}
                onChange={(e) => setRegisterForm({ ...registerForm, password: e.target.value })}
                required
                minLength={8}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-confirm-password">确认密码</Label>
              <Input
                id="register-confirm-password"
                type="password"
                placeholder="请再次输入密码"
                value={registerForm.confirmPassword}
                onChange={(e) => setRegisterForm({ ...registerForm, confirmPassword: e.target.value })}
                required
              />
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isEmailLoading || isGithubLoading || isGoogleLoading}
            >
              {isEmailLoading ? (
                <ButtonLoading text="注册中..." />
              ) : (
                "注册"
              )}
            </Button>
          </form>



          <div className="mt-6 text-center">
            <span className="text-sm text-gray-600">已有账户？</span>
            <Link href="/login" className="text-sm text-blue-600 hover:underline ml-1">
              立即登录
            </Link>
          </div>

        </CardContent>

      </Card>
      <div className="mt-4 text-xs text-gray-500 text-center">
        注册代表同意
        <Link href="/terms" target="_blank" className="text-blue-600 hover:underline mx-1">
          服务条款
        </Link>
        和
        <Link href="/privacy" target="_blank" className="text-blue-600 hover:underline mx-1">
          隐私政策
        </Link>
      </div>
    </>
  )
}
