'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Tag, 
  Calendar,
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'
import type { CategoryWithStats, CategoryFormData } from '@/types'

interface CategoryListProps {
  initialData: CategoryWithStats[]
}

export function CategoryList({ initialData }: CategoryListProps) {
  const [categories, setCategories] = useState<CategoryWithStats[]>(initialData)
  const [loading, setLoading] = useState(false)
  
  // 对话框状态
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  
  // 表单状态
  const [formData, setFormData] = useState<CategoryFormData>({ name: '' })
  const [editingCategory, setEditingCategory] = useState<CategoryWithStats | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<CategoryWithStats | null>(null)

  // 重新加载数据
  const reloadData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories')
      const result = await response.json()
      
      if (result.success) {
        setCategories(result.data)
      } else {
        toast.error(result.error || '加载数据失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      toast.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 创建分类
  const handleCreate = async () => {
    if (!formData.name.trim()) {
      toast.error('请输入分类名称')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message || '分类创建成功')
        setCreateDialogOpen(false)
        setFormData({ name: '' })
        await reloadData()
      } else {
        toast.error(result.error || '创建失败')
      }
    } catch (error) {
      console.error('创建分类失败:', error)
      toast.error('创建分类失败')
    } finally {
      setLoading(false)
    }
  }

  // 编辑分类
  const handleEdit = async () => {
    if (!editingCategory || !formData.name.trim()) {
      toast.error('请输入分类名称')
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/categories/${editingCategory.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message || '分类更新成功')
        setEditDialogOpen(false)
        setEditingCategory(null)
        setFormData({ name: '' })
        await reloadData()
      } else {
        toast.error(result.error || '更新失败')
      }
    } catch (error) {
      console.error('更新分类失败:', error)
      toast.error('更新分类失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除分类
  const handleDelete = async () => {
    if (!deletingCategory) return

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/categories/${deletingCategory.id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message || '分类删除成功')
        setDeleteDialogOpen(false)
        setDeletingCategory(null)
        await reloadData()
      } else {
        toast.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      toast.error('删除分类失败')
    } finally {
      setLoading(false)
    }
  }

  // 打开编辑对话框
  const openEditDialog = (category: CategoryWithStats) => {
    setEditingCategory(category)
    setFormData({ name: category.name })
    setEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (category: CategoryWithStats) => {
    setDeletingCategory(category)
    setDeleteDialogOpen(true)
  }

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                分类管理
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                管理所有 prompt 分类，支持创建、编辑和删除操作
              </p>
            </div>
            <Button 
              onClick={() => setCreateDialogOpen(true)}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              添加分类
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* 分类列表 */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>分类名称</TableHead>
                  <TableHead>Prompt 数量</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{category.name}</span>
                        {category.name === '未分类' && (
                          <Badge variant="secondary" className="text-xs">
                            默认
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={category.promptCount > 0 ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {category.promptCount}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {formatDate(category.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {formatDate(category.updatedAt)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openEditDialog(category)}
                          disabled={loading}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openDeleteDialog(category)}
                          disabled={loading || category.name === '未分类'}
                          className={category.name === '未分类' ? 'opacity-50' : ''}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 创建分类对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>创建新分类</DialogTitle>
            <DialogDescription>
              为 prompts 创建一个新的分类。分类名称必须唯一。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="create-name">分类名称</Label>
              <Input
                id="create-name"
                value={formData.name}
                onChange={(e) => setFormData({ name: e.target.value })}
                placeholder="请输入分类名称"
                disabled={loading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setCreateDialogOpen(false)
                setFormData({ name: '' })
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button onClick={handleCreate} disabled={loading}>
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              创建
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑分类对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑分类</DialogTitle>
            <DialogDescription>
              修改分类名称。新名称不能与现有分类重复。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">分类名称</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ name: e.target.value })}
                placeholder="请输入分类名称"
                disabled={loading}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setEditDialogOpen(false)
                setEditingCategory(null)
                setFormData({ name: '' })
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button onClick={handleEdit} disabled={loading}>
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除分类确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              确认删除分类
            </DialogTitle>
            <DialogDescription>
              您确定要删除分类 “{deletingCategory?.name}” 吗？
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-yellow-800">
                    删除影响
                  </p>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>该分类下的 {deletingCategory?.promptCount || 0} 个 prompts 将移动到“未分类”</li>
                    <li>此操作不可撤销</li>
                    <li> “未分类”分类不能被删除</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false)
                setDeletingCategory(null)
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
