'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'
import {
  Eye,
  EyeOff,
  Tag,
  Search,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Calendar,
  User,
  Star
} from 'lucide-react'
import type { AdminPromptWithUser, PaginatedResult } from '@/lib/admin-prompts'
import { PromptDetailModal } from './prompt-detail-modal'

interface PromptListProps {
  initialData?: PaginatedResult<AdminPromptWithUser>
  categories?: string[]
}

export function PromptList({ initialData, categories = [] }: PromptListProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [data, setData] = useState<PaginatedResult<AdminPromptWithUser> | null>(initialData || null)
  const [loading, setLoading] = useState(!initialData)
  const [selectedPrompts, setSelectedPrompts] = useState<number[]>([])
  const [availableCategories, setAvailableCategories] = useState<string[]>(categories)

  // 从 URL 参数初始化状态
  const [search, setSearch] = useState(searchParams.get('search') || '')
  const [category, setCategory] = useState(searchParams.get('category') || '全部')
  const [sortBy, setSortBy] = useState<'createdAt' | 'updatedAt' | 'name'>(
    (searchParams.get('sortBy') as 'createdAt' | 'updatedAt' | 'name') || 'createdAt'
  )
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
  )
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get('page') || '1')
  )
  const [pageSize] = useState(10)

  // 更新 URL 参数
  const updateURL = useCallback((params: Record<string, string | number>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString())

    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '全部' && value !== '') {
        newSearchParams.set(key, value.toString())
      } else {
        newSearchParams.delete(key)
      }
    })

    // 如果页码是 1，则删除 page 参数
    if (params.page === 1) {
      newSearchParams.delete('page')
    }

    const newURL = `/admin/prompts${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ''}`
    router.push(newURL, { scroll: false })
  }, [router, searchParams])

  // 加载数据
  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sortBy,
        sortOrder,
      })

      if (search) params.append('search', search)
      if (category !== '全部') params.append('category', category)

      const response = await fetch(`/api/admin/prompts?${params}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data)
        if (result.categories && result.categories.length > 0) {
          setAvailableCategories(result.categories)
        }
      } else {
        toast.error(result.error || '加载数据失败')
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      toast.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, sortBy, sortOrder, search, category])

  // 初始加载和依赖更新
  useEffect(() => {
    loadData()
  }, [loadData])

  // 处理分页变化
  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage)
    updateURL({
      page: newPage,
      search,
      category: category !== '全部' ? category : '',
      sortBy,
      sortOrder,
    })
  }, [search, category, sortBy, sortOrder, updateURL])

  // 处理搜索变化
  const handleSearchChange = useCallback((newSearch: string) => {
    setSearch(newSearch)
  }, [])

  // 搜索防抖效果
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1) // 重置到第一页
      updateURL({
        page: 1,
        search,
        category: category !== '全部' ? category : '',
        sortBy,
        sortOrder,
      })
    }, 500) // 500ms 防抖延迟

    return () => clearTimeout(timeoutId)
  }, [search, category, sortBy, sortOrder, updateURL])

  // 处理分类变化
  const handleCategoryChange = useCallback((newCategory: string) => {
    setCategory(newCategory)
    setCurrentPage(1) // 重置到第一页
    updateURL({
      page: 1,
      search,
      category: newCategory !== '全部' ? newCategory : '',
      sortBy,
      sortOrder,
    })
  }, [search, sortBy, sortOrder, updateURL])

  // 处理排序变化
  const handleSortChange = useCallback((newSortBy: string, newSortOrder?: string) => {
    const finalSortBy = newSortBy as 'createdAt' | 'updatedAt' | 'name'
    const finalSortOrder = newSortOrder as 'asc' | 'desc' || sortOrder

    setSortBy(finalSortBy)
    if (newSortOrder) setSortOrder(finalSortOrder)
    setCurrentPage(1) // 重置到第一页

    updateURL({
      page: 1,
      search,
      category: category !== '全部' ? category : '',
      sortBy: finalSortBy,
      sortOrder: finalSortOrder,
    })
  }, [search, category, sortOrder, updateURL])

  // 更新隐私设置
  const updatePrivacy = async (promptId: number, isPublic: boolean) => {
    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updatePrivacy',
          promptId,
          isPublic,
        }),
      })

      const result = await response.json()
      if (result.success) {
        toast.success(result.message)
        loadData()
      } else {
        toast.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('更新隐私设置失败:', error)
      toast.error('操作失败')
    }
  }

  // 更新分类
  const updateCategory = async (promptId: number, newCategory: string) => {
    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateCategory',
          promptId,
          category: newCategory,
        }),
      })

      const result = await response.json()
      if (result.success) {
        toast.success(result.message)
        loadData()
      } else {
        toast.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('更新分类失败:', error)
      toast.error('操作失败')
    }
  }

  // 更新推荐状态
  const updateRecommended = async (promptId: number, isRecommended: boolean) => {
    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateRecommended',
          promptId,
          isRecommended,
        }),
      })

      const result = await response.json()
      if (result.success) {
        toast.success(result.message)
        loadData()
      } else {
        toast.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('更新推荐状态失败:', error)
      toast.error('操作失败')
    }
  }

  // 批量操作
  const batchUpdatePrivacy = async (isPublic: boolean) => {
    if (selectedPrompts.length === 0) {
      toast.error('请先选择要操作的 prompts')
      return
    }

    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'batchUpdatePrivacy',
          promptIds: selectedPrompts,
          isPublic,
        }),
      })

      const result = await response.json()
      if (result.success) {
        toast.success(result.message)
        setSelectedPrompts([])
        loadData()
      } else {
        toast.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      toast.error('操作失败')
    }
  }

  // 批量更新推荐状态
  const batchUpdateRecommended = async (isRecommended: boolean) => {
    if (selectedPrompts.length === 0) {
      toast.error('请先选择要操作的 prompts')
      return
    }

    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'batchUpdateRecommended',
          promptIds: selectedPrompts,
          isRecommended,
        }),
      })

      const result = await response.json()
      if (result.success) {
        toast.success(result.message)
        setSelectedPrompts([])
        loadData()
      } else {
        toast.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      toast.error('操作失败')
    }
  }

  // 格式化日期
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </CardContent>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-600">暂无数据</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 搜索和过滤 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            搜索和过滤
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">搜索</Label>
              <Input
                id="search"
                placeholder="搜索 prompt 名称或描述..."
                value={search}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="category">分类</Label>
              <Select value={category} onValueChange={handleCategoryChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="全部">全部分类</SelectItem>
                  {availableCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="sortBy">排序方式</Label>
              <Select value={sortBy} onValueChange={(value: any) => handleSortChange(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">创建时间</SelectItem>
                  <SelectItem value="updatedAt">更新时间</SelectItem>
                  <SelectItem value="name">名称</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="sortOrder">排序顺序</Label>
              <Select value={sortOrder} onValueChange={(value: any) => handleSortChange(sortBy, value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">降序</SelectItem>
                  <SelectItem value="asc">升序</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作 */}
      {selectedPrompts.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                已选择 {selectedPrompts.length} 个 prompt
              </span>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => batchUpdatePrivacy(false)}
                >
                  <EyeOff className="h-4 w-4 mr-2" />
                  批量隐藏
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => batchUpdateRecommended(true)}
                >
                  <Star className="h-4 w-4 mr-2" />
                  批量推荐
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => batchUpdateRecommended(false)}
                >
                  <Star className="h-4 w-4 mr-2" />
                  取消推荐
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedPrompts([])}
                >
                  取消选择
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle>Prompt 列表</CardTitle>
          <p className="text-sm text-gray-600">
            共 {data.total} 个公开 prompt，当前第 {data.page} 页
          </p>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedPrompts.length === data.data.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedPrompts(data.data.map(p => p.id))
                        } else {
                          setSelectedPrompts([])
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>标题</TableHead>
                  <TableHead>作者</TableHead>
                  <TableHead>分类</TableHead>
                  <TableHead>推荐状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.map((prompt) => (
                  <TableRow key={prompt.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedPrompts.includes(prompt.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPrompts([...selectedPrompts, prompt.id])
                          } else {
                            setSelectedPrompts(selectedPrompts.filter(id => id !== prompt.id))
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{prompt.name}</div>
                        {prompt.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {prompt.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium">{prompt.user.name}</div>
                          <div className="text-xs text-gray-500">{prompt.user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={prompt.category?.name || '未分类'}
                        onValueChange={(value) => updateCategory(prompt.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableCategories.map((cat) => (
                            <SelectItem key={cat} value={cat}>
                              {cat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={prompt.isRecommended || false}
                          onCheckedChange={(checked) => updateRecommended(prompt.id, checked)}
                        />
                        <span className="text-sm text-gray-600">
                          {prompt.isRecommended ? '已推荐' : '未推荐'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        {formatDate(prompt.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <PromptDetailModal
                          prompt={prompt}
                          trigger={
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                          }
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updatePrivacy(prompt.id, false)}
                        >
                          <EyeOff className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-600">
              显示第 {(data.page - 1) * data.limit + 1} - {Math.min(data.page * data.limit, data.total)} 条，
              共 {data.total} 条记录
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={data.page <= 1}
                onClick={() => handlePageChange(data.page - 1)}
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>
              <span className="text-sm">
                第 {data.page} 页，共 {data.totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={data.page >= data.totalPages}
                onClick={() => handlePageChange(data.page + 1)}
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
