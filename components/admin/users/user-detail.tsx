'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { ArrowLeft, Mail, Calendar, Clock, Shield, User, Edit2, Save, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { UserAvatar } from './user-avatar'
import { Loading } from '@/components/ui/loading'
import { toast } from 'sonner'
import type { AdminUser } from '@/types/admin'

interface UserDetailProps {
  userId: string
  currentUserId: string
}

export function UserDetail({ userId, currentUserId }: UserDetailProps) {
  const router = useRouter()
  const [user, setUser] = useState<AdminUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    isAdmin: false
  })

  // 获取用户详情
  const fetchUser = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/users/${userId}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('用户不存在')
          router.push('/admin/users')
          return
        }
        throw new Error('获取用户详情失败')
      }

      const userData: AdminUser = await response.json()
      setUser(userData)
      setEditForm({
        name: userData.name,
        email: userData.email,
        isAdmin: userData.isAdmin
      })
    } catch (error) {
      console.error('Fetch user failed:', error)
      toast.error('获取用户详情失败')
    } finally {
      setLoading(false)
    }
  }, [userId, router])

  // 保存用户信息
  const handleSave = async () => {
    if (!user) return

    // 防止管理员取消自己的管理员权限
    if (!editForm.isAdmin && userId === currentUserId) {
      toast.error('不能取消自己的管理员权限')
      return
    }

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '更新失败')
      }

      const updatedUser: AdminUser = await response.json()
      setUser(updatedUser)
      setEditing(false)
      toast.success('用户信息更新成功')
    } catch (error) {
      console.error('Update user failed:', error)
      toast.error(error instanceof Error ? error.message : '更新用户信息失败')
    } finally {
      setSaving(false)
    }
  }

  // 取消编辑
  const handleCancel = () => {
    if (user) {
      setEditForm({
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin
      })
    }
    setEditing(false)
  }

  useEffect(() => {
    fetchUser()
  }, [fetchUser])

  const formatDate = (date: Date | string) => {
    return format(new Date(date), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">用户不存在</p>
        <Button
          variant="outline"
          onClick={() => router.push('/admin/users')}
          className="mt-4"
        >
          返回用户列表
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">用户详情</h2>
          </div>
        </div>

        <div className="flex space-x-2">
          {editing ? (
            <>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving}
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? '保存中...' : '保存'}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setEditing(true)}
            >
              <Edit2 className="h-4 w-4 mr-2" />
              编辑
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 基本信息 */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">基本信息</h3>
            
            <div className="space-y-4">
              {/* 头像和基本信息 */}
              <div className="flex items-center space-x-4">
                <UserAvatar user={user} size="lg" />
                <div>
                  <h4 className="text-xl font-semibold">{user.name}</h4>
                  <p className="text-gray-600">{user.email}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant={user.isAdmin ? 'default' : 'secondary'}>
                      {user.isAdmin ? '管理员' : '普通用户'}
                    </Badge>
                    <Badge variant={user.emailVerified ? 'default' : 'secondary'}>
                      {user.emailVerified ? '邮箱已验证' : '邮箱未验证'}
                    </Badge>
                  </div>
                </div>
              </div>

              <Separator />

              {/* 编辑表单 */}
              {editing ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">姓名</Label>
                    <Input
                      id="name"
                      value={editForm.name}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                      placeholder="请输入姓名"
                    />
                  </div>

                  <div>
                    <Label htmlFor="email">邮箱</Label>
                    <Input
                      id="email"
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                      placeholder="请输入邮箱"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isAdmin"
                      checked={editForm.isAdmin}
                      onCheckedChange={(checked) => setEditForm({ ...editForm, isAdmin: checked })}
                      disabled={userId === currentUserId && editForm.isAdmin}
                    />
                    <Label htmlFor="isAdmin">管理员权限</Label>
                    {userId === currentUserId && editForm.isAdmin && (
                      <span className="text-sm text-gray-500">（不能取消自己的管理员权限）</span>
                    )}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">用户ID</Label>
                    <p className="mt-1 font-mono text-sm">{user.id}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">姓名</Label>
                    <p className="mt-1">{user.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">邮箱</Label>
                    <p className="mt-1">{user.email}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">角色</Label>
                    <p className="mt-1">{user.isAdmin ? '管理员' : '普通用户'}</p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* 状态信息 */}
        <div>
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">状态信息</h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Mail className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">邮箱验证</p>
                  <p className="text-sm text-gray-600">
                    {user.emailVerified ? '已验证' : '未验证'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Shield className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">账户状态</p>
                  <p className="text-sm text-gray-600">{user.status === 'active' ? '活跃' : '非活跃'}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">注册时间</p>
                  <p className="text-sm text-gray-600">{formatDate(user.createdAt)}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">最后登录</p>
                  <p className="text-sm text-gray-600">
                    {user.lastLoginAt ? formatDate(user.lastLoginAt) : '从未登录'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">更新时间</p>
                  <p className="text-sm text-gray-600">{formatDate(user.updatedAt)}</p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
