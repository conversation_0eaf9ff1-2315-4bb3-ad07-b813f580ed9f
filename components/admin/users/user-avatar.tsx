'use client'

import { useState } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import type { AdminUser } from '@/types/admin'

interface UserAvatarProps {
  user: Pick<AdminUser, 'name' | 'image'>
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const sizeClasses = {
  sm: 'h-8 w-8 text-sm',
  md: 'h-12 w-12 text-base',
  lg: 'h-16 w-16 text-xl'
}

export function UserAvatar({ user, size = 'md', className }: UserAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  // 如果没有图片或图片加载失败，显示默认头像
  if (!user.image || imageError) {
    return (
      <div className={cn(
        'rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0',
        sizeClasses[size],
        className
      )}>
        <span className="font-medium text-gray-600">
          {user.name.charAt(0).toUpperCase()}
        </span>
      </div>
    )
  }

  return (
    <div className={cn('relative rounded-full overflow-hidden flex-shrink-0', sizeClasses[size], className)}>
      {imageLoading && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <span className="font-medium text-gray-600">
            {user.name.charAt(0).toUpperCase()}
          </span>
        </div>
      )}
      <Image
        src={user.image}
        alt={user.name}
        width={size === 'sm' ? 32 : size === 'md' ? 48 : 64}
        height={size === 'sm' ? 32 : size === 'md' ? 48 : 64}
        className={cn(
          'rounded-full object-cover transition-opacity',
          imageLoading ? 'opacity-0' : 'opacity-100'
        )}
        onError={handleImageError}
        onLoad={handleImageLoad}
        unoptimized={false}
      />
    </div>
  )
}
