'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Eye, Mail, Calendar, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { UserSearch } from './user-search'
import { UserStatusToggle } from './user-status-toggle'
import { UserAvatar } from './user-avatar'
import { Loading } from '@/components/ui/loading'
import { toast } from 'sonner'
import type { AdminUser, UserListResponse } from '@/types/admin'

interface UserListProps {
  currentUserId: string
}

export function UserList({ currentUserId }: UserListProps) {
  const [users, setUsers] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [searchParams, setSearchParams] = useState({
    search: '',
    status: 'all',
    role: 'all'
  })

  // 获取用户列表
  const fetchUsers = useCallback(async (params = searchParams, page = pagination.page) => {
    setLoading(true)
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...params
      })

      const response = await fetch(`/api/admin/users?${queryParams}`)
      if (!response.ok) {
        throw new Error('获取用户列表失败')
      }

      const data: UserListResponse = await response.json()
      setUsers(data.users)
      setPagination({
        page: data.page,
        limit: data.limit,
        total: data.total,
        totalPages: data.totalPages
      })
    } catch (error) {
      console.error('Fetch users failed:', error)
      toast.error('获取用户列表失败')
    } finally {
      setLoading(false)
    }
  }, [pagination.limit, pagination.page, searchParams])

  // 搜索处理
  const handleSearch = (params: typeof searchParams) => {
    setSearchParams(params)
    fetchUsers(params, 1)
  }

  // 切换管理员状态
  const handleToggleAdmin = async (userId: string, isAdmin: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isAdmin }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '更新失败')
      }

      // 刷新用户列表
      await fetchUsers()
    } catch (error) {
      throw error
    }
  }

  // 分页处理
  const handlePageChange = (newPage: number) => {
    fetchUsers(searchParams, newPage)
  }

  // 初始加载
  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  const formatDate = (date: Date | string) => {
    return format(new Date(date), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
  }

  if (loading && users.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 搜索组件 */}
      <UserSearch onSearch={handleSearch} loading={loading} />

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Mail className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">总用户数</p>
              <p className="text-2xl font-bold">{pagination.total}</p>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-green-100 rounded-lg">
              <Badge className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">管理员</p>
              <p className="text-2xl font-bold">
                {users.filter(u => u.isAdmin).length}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">活跃用户</p>
              <p className="text-2xl font-bold">
                {users.filter(u => u.status === 'active').length}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">本页显示</p>
              <p className="text-2xl font-bold">{users.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* 用户表格 */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">用户列表</h3>
            <div className="text-sm text-gray-500">
              共 {pagination.total} 个用户，第 {pagination.page} / {pagination.totalPages} 页
            </div>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户信息</TableHead>
                  <TableHead className="hidden sm:table-cell">邮箱验证</TableHead>
                  <TableHead>角色权限</TableHead>
                  <TableHead className="hidden md:table-cell">注册时间</TableHead>
                  <TableHead className="hidden lg:table-cell">最后登录</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <UserAvatar user={user} size="sm" />
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <Badge variant={user.emailVerified ? 'default' : 'secondary'}>
                        {user.emailVerified ? '已验证' : '未验证'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <UserStatusToggle
                        userId={user.id}
                        isAdmin={user.isAdmin}
                        currentUserId={currentUserId}
                        onToggle={handleToggleAdmin}
                      />
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="text-sm">
                        {formatDate(user.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="text-sm">
                        {user.lastLoginAt
                          ? formatDate(user.lastLoginAt)
                          : '从未登录'
                        }
                      </div>
                    </TableCell>
                    <TableCell>
                      <Link href={`/admin/users/${user.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          查看详情
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1 || loading}
              >
                上一页
              </Button>
              
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <Button
                      key={page}
                      variant={page === pagination.page ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      disabled={loading}
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages || loading}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      </Card>
    </div>
  )
}
