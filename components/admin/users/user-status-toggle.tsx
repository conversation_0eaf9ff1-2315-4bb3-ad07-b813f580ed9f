'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'

interface UserStatusToggleProps {
  userId: string
  isAdmin: boolean
  currentUserId: string
  onToggle: (userId: string, isAdmin: boolean) => Promise<void>
}

export function UserStatusToggle({ 
  userId, 
  isAdmin, 
  currentUserId, 
  onToggle 
}: UserStatusToggleProps) {
  const [loading, setLoading] = useState(false)
  const [adminStatus, setAdminStatus] = useState(isAdmin)

  const handleToggle = async (checked: boolean) => {
    // 防止管理员取消自己的管理员权限
    if (!checked && userId === currentUserId) {
      toast.error('不能取消自己的管理员权限')
      return
    }

    setLoading(true)
    try {
      await onToggle(userId, checked)
      setAdminStatus(checked)
      toast.success(checked ? '已设置为管理员' : '已取消管理员权限')
    } catch (error) {
      toast.error('操作失败，请重试')
      console.error('Toggle admin status failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex items-center space-x-2">
      <Switch
        checked={adminStatus}
        onCheckedChange={handleToggle}
        disabled={loading || (userId === currentUserId && adminStatus)}
      />
      <Badge variant={adminStatus ? 'default' : 'secondary'}>
        {adminStatus ? '管理员' : '普通用户'}
      </Badge>
    </div>
  )
}
