'use client'

import { ReactNode } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  FileText,
  Users,
  BarChart3,
  Shield,
  ArrowLeft,
  Tag
} from 'lucide-react'

interface AdminLayoutProps {
  children: ReactNode
  title?: string
  description?: string
}

const adminNavItems = [
  {
    title: 'Prompt 管理',
    href: '/admin/prompts',
    icon: FileText,
  },
  {
    title: '分类管理',
    href: '/admin/categories',
    icon: Tag,
  },
  {
    title: '用户管理',
    href: '/admin/users',
    icon: Users,
  },
  {
    title: '使用统计',
    href: '/admin/chat-usage',
    icon: BarChart3,
  }
]

export function AdminLayout({ children, title, description }: AdminLayoutProps) {
  const pathname = usePathname()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 - 固定定位 */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 border-b border-gray-200 fixed-header">
        <div className="mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/prompts">
                <Button variant="ghost" size="sm" className="text-gray-600">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回主站
                </Button>
              </Link>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">管理后台</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">管理员模式</span>
            </div>
          </div>
        </div>
      </header>

      {/* 主布局容器 - 添加顶部边距为固定header预留空间 */}
      <div className="flex min-h-screen pt-16">
        {/* 侧边栏 - 调整sticky定位的top值 */}
        <aside className="w-64 bg-white border-r border-gray-200 flex-shrink-0 sticky top-16 h-[calc(100vh-4rem)]">
          <nav className="h-full overflow-y-auto py-6 scrollbar-thin">
            <div className="px-4 space-y-1">
              {adminNavItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors group',
                      isActive
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    )}
                  >
                    <Icon className={cn(
                      'h-5 w-5 mr-3 flex-shrink-0',
                      isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-600'
                    )} />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{item.title}</div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </nav>
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 bg-gray-50">
          <div className="p-6 lg:p-8">
            {(title || description) && (
              <div className="mb-8">
                {title && (
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {title}
                  </h2>
                )}
                {description && (
                  <p className="text-gray-600">
                    {description}
                  </p>
                )}
              </div>
            )}
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
