'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  MessageCircle, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  RefreshCw,
  RotateCcw,
  Activity
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { toast } from 'sonner';

interface UsageOverview {
  totalUsers: number;
  activeUsersToday: number;
  totalChatsToday: number;
  averageChatsPerUser: number;
  usersAtLimit: number;
}

interface UserUsage {
  userId: string;
  userName: string;
  userEmail: string;
  count: number;
  limit: number;
  remainingCount: number;
}

export function ChatUsageManagement() {
  const [overview, setOverview] = useState<UsageOverview | null>(null);
  const [userUsages, setUserUsages] = useState<UserUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取概览数据
      const overviewResponse = await fetch('/api/admin/chat-usage?action=overview');

      if (overviewResponse.ok) {
        const overviewResult = await overviewResponse.json();
        if (overviewResult.success && overviewResult.data) {
          setOverview(overviewResult.data);
        } else {
          console.error('❌ 概览数据格式错误:', overviewResult);
          setError('概览数据格式错误');
        }
      } else {
        const errorText = await overviewResponse.text();
        console.error('❌ 概览数据获取失败:', overviewResponse.status, errorText);
        const errorMsg = `获取概览数据失败: ${overviewResponse.status}`;
        setError(errorMsg);
        toast.error(errorMsg);
      }

      // 获取用户使用数据
      const usageResponse = await fetch('/api/admin/chat-usage');

      if (usageResponse.ok) {
        const usageResult = await usageResponse.json();
        if (usageResult.success && usageResult.data) {
          setUserUsages(usageResult.data);
        } else {
          console.error('❌ 用户数据格式错误:', usageResult);
          setError('用户数据格式错误');
        }
      } else {
        const errorText = await usageResponse.text();
        console.error('❌ 用户数据获取失败:', usageResponse.status, errorText);
        const errorMsg = `获取用户数据失败: ${usageResponse.status}`;
        setError(errorMsg);
        toast.error(errorMsg);
      }

    } catch (error) {
      console.error('❌ 获取数据失败:', error);
      const errorMsg = '获取数据失败: ' + (error instanceof Error ? error.message : '未知错误');
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
    toast.success('数据已刷新');
  };

  const resetDailyUsage = async () => {
    if (!confirm('确定要重置今日所有用户的使用次数吗？此操作不可撤销。')) {
      return;
    }

    try {
      setResetting(true);
      const response = await fetch('/api/admin/chat-usage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reset',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
        await fetchData(); // 刷新数据
      } else {
        const error = await response.json();
        toast.error(error.error || '重置失败');
      }
    } catch (error) {
      console.error('重置失败:', error);
      toast.error('重置失败');
    } finally {
      setResetting(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="flex items-center gap-2 text-red-600">
          <AlertTriangle className="h-6 w-6" />
          <span className="text-lg font-medium">加载失败</span>
        </div>
        <p className="text-muted-foreground text-center">{error}</p>
        <Button onClick={fetchData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gray-100 p-4 rounded text-sm">
          <strong>调试信息:</strong>
          加载中: {loading ? '是' : '否'},
          错误: {error || '无'},
          概览数据: {overview ? '已加载' : '未加载'},
          用户数据: {userUsages.length} 条
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button 
          onClick={refreshData} 
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          刷新数据
        </Button>
        <Button 
          onClick={resetDailyUsage} 
          disabled={resetting}
          variant="destructive"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          重置今日使用次数
        </Button>
      </div>

      {/* 概览统计 */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总用户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalUsers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">今日活跃用户</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.activeUsersToday}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">今日总对话</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalChatsToday}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均对话数</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.averageChatsPerUser.toFixed(1)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">达到限额用户</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {overview.usersAtLimit}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 用户使用详情 */}
      <Card>
        <CardHeader>
          <CardTitle>用户使用详情</CardTitle>
          <p className="text-sm text-muted-foreground">
            今日各用户的AI对话使用情况
          </p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户</TableHead>
                <TableHead>邮箱</TableHead>
                <TableHead>使用次数</TableHead>
                <TableHead>使用进度</TableHead>
                <TableHead>状态</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {userUsages.map((usage) => {
                const percentage = (usage.count / usage.limit) * 100;
                const isAtLimit = usage.remainingCount === 0;
                const isNearLimit = usage.remainingCount <= 2 && usage.remainingCount > 0;

                return (
                  <TableRow key={usage.userId}>
                    <TableCell className="font-medium">
                      {usage.userName}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {usage.userEmail}
                    </TableCell>
                    <TableCell>
                      <span className="font-mono">
                        {usage.count} / {usage.limit}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Progress 
                          value={percentage} 
                          className="h-2 w-20"
                          indicatorClassName={
                            isAtLimit ? 'bg-red-500' : 
                            isNearLimit ? 'bg-orange-500' : 
                            'bg-green-500'
                          }
                        />
                        <div className="text-xs text-muted-foreground">
                          {percentage.toFixed(0)}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          isAtLimit ? "destructive" : 
                          isNearLimit ? "secondary" : 
                          usage.count > 0 ? "default" : "outline"
                        }
                      >
                        {isAtLimit ? "已用完" : 
                         isNearLimit ? "即将用完" : 
                         usage.count > 0 ? "正常使用" : "未使用"}
                      </Badge>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {userUsages.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              暂无使用数据
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
