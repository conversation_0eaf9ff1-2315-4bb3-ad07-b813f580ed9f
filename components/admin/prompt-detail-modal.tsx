'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { 
  Eye, 
  User, 
  Calendar, 
  Tag, 
  FileText, 
  Settings,
  Copy,
  ExternalLink
} from 'lucide-react'
import { toast } from 'sonner'
import type { AdminPromptWithUser } from '@/lib/admin-prompts'

interface PromptDetailModalProps {
  prompt: AdminPromptWithUser
  trigger?: React.ReactNode
}

export function PromptDetailModal({ prompt, trigger }: PromptDetailModalProps) {
  const [open, setOpen] = useState(false)

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('已复制到剪贴板')
    }).catch(() => {
      toast.error('复制失败')
    })
  }

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Eye className="h-4 w-4 mr-2" />
      查看详情
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] sm:max-w-[90vw] md:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2 text-base sm:text-lg">
            <FileText className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
            <span className="truncate">{prompt.name}</span>
          </DialogTitle>
          <DialogDescription className="text-xs sm:text-sm">
            Prompt 详细信息和内容预览
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 sm:space-y-6 px-1">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <Settings className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">Prompt ID</label>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs sm:text-sm font-mono bg-gray-100 px-2 py-1 rounded truncate">
                      #{prompt.id}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex-shrink-0"
                      onClick={() => copyToClipboard(prompt.id.toString())}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">分类</label>
                  <div className="mt-1">
                    <Badge variant="secondary" className="flex items-center gap-1 w-fit max-w-full">
                      <Tag className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{prompt.category?.name || '未分类'}</span>
                    </Badge>
                  </div>
                </div>

                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">可见性</label>
                  <div className="mt-1">
                    <Badge
                      variant={prompt.isPublic ? "default" : "secondary"}
                      className="flex items-center gap-1 w-fit"
                    >
                      <Eye className="h-3 w-3 flex-shrink-0" />
                      {prompt.isPublic ? '公开' : '私密'}
                    </Badge>
                  </div>
                </div>

                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">参数数量</label>
                  <div className="mt-1">
                    <span className="text-xs sm:text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {prompt.parameters.length} 个参数
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">创建时间</label>
                  <div className="flex items-center gap-2 mt-1 text-xs sm:text-sm text-gray-700">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="truncate">{formatDate(prompt.createdAt)}</span>
                  </div>
                </div>

                <div className="min-w-0">
                  <label className="text-xs sm:text-sm font-medium text-gray-600">更新时间</label>
                  <div className="flex items-center gap-2 mt-1 text-xs sm:text-sm text-gray-700">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="truncate">{formatDate(prompt.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 作者信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                作者信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3 sm:gap-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="font-medium text-sm sm:text-base truncate">{prompt.user.name}</div>
                  <div className="text-xs sm:text-sm text-gray-600 truncate">{prompt.user.email}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    用户 ID: {prompt.user.id}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 描述 */}
          {prompt.description && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">描述</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs sm:text-sm text-gray-700 whitespace-pre-wrap break-words">
                  {prompt.description}
                </p>
              </CardContent>
            </Card>
          )}

          {/* 参数列表 */}
          {prompt.parameters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">参数列表</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {prompt.parameters.map((param, index) => (
                    <div key={param.id} className="border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-center justify-between mb-2 gap-2">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <span className="font-medium text-sm sm:text-base truncate">{param.name}</span>
                          {param.required && (
                            <Badge variant="destructive" className="text-xs flex-shrink-0">
                              必需
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 flex-shrink-0">#{index + 1}</span>
                      </div>
                      {param.description && (
                        <p className="text-xs sm:text-sm text-gray-600 mt-1 break-words">
                          {param.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Prompt 内容 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg flex items-center justify-between gap-2">
                <span className="truncate">Prompt 内容</span>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-shrink-0"
                  onClick={() => copyToClipboard(prompt.content)}
                >
                  <Copy className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">复制内容</span>
                  <span className="sm:hidden">复制</span>
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 max-h-64 sm:max-h-96 overflow-y-auto">
                <pre className="text-xs sm:text-sm text-gray-800 whitespace-pre-wrap font-mono break-words overflow-wrap-anywhere">
                  {prompt.content}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4 border-t flex-shrink-0">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              className="w-full sm:w-auto"
            >
              关闭
            </Button>
            <Button
              variant="outline"
              className="w-full sm:w-auto"
              onClick={() => {
                // 这里可以添加跳转到编辑页面的逻辑
                window.open(`/prompts/detail/${prompt.id}`, '_blank')
              }}
            >
              <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">在新窗口查看</span>
              <span className="sm:hidden">新窗口查看</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
