# Loading 组件使用示例

## 基础用法

### 1. 页面级 Loading
用于整个页面的加载状态：

```tsx
import { PageLoading } from '@/components/ui/loading'

function MyPage() {
  const [isLoading, setIsLoading] = useState(true)
  
  if (isLoading) {
    return <PageLoading text="加载中..." />
  }
  
  return <div>页面内容</div>
}
```

### 2. 按钮内 Loading
用于按钮的加载状态：

```tsx
import { ButtonLoading } from '@/components/ui/loading'
import { Button } from '@/components/ui/button'

function MyButton() {
  const [isLoading, setIsLoading] = useState(false)
  
  return (
    <Button disabled={isLoading}>
      {isLoading ? (
        <ButtonLoading text="提交中..." />
      ) : (
        "提交"
      )}
    </Button>
  )
}
```

### 3. 卡片内 Loading
用于卡片或容器内的加载状态：

```tsx
import { CardLoading } from '@/components/ui/loading'
import { Card, CardContent } from '@/components/ui/card'

function MyCard() {
  const [isLoading, setIsLoading] = useState(true)
  
  return (
    <Card>
      <CardContent>
        {isLoading ? (
          <CardLoading text="数据加载中..." />
        ) : (
          <div>卡片内容</div>
        )}
      </CardContent>
    </Card>
  )
}
```

### 4. 内联 Loading
用于行内的加载状态：

```tsx
import { InlineLoading } from '@/components/ui/loading'

function MyComponent() {
  const [isLoading, setIsLoading] = useState(false)
  
  return (
    <div>
      {isLoading && <InlineLoading text="处理中..." />}
    </div>
  )
}
```

## 高级用法

### 自定义 Loading 组件
使用基础 Loading 组件创建自定义样式：

```tsx
import { Loading } from '@/components/ui/loading'
import { RefreshCw } from 'lucide-react'

function CustomLoading() {
  return (
    <Loading
      size="lg"
      text="自定义加载中..."
      direction="vertical"
      icon={<RefreshCw className="h-8 w-8 animate-spin text-blue-500" />}
      textClassName="text-blue-600"
      className="bg-blue-50 p-4 rounded-lg"
    />
  )
}
```

### 不同尺寸
```tsx
<Loading size="sm" text="小尺寸" />
<Loading size="md" text="中等尺寸" />
<Loading size="lg" text="大尺寸" />
```

### 不同方向
```tsx
<Loading direction="horizontal" text="水平布局" />
<Loading direction="vertical" text="垂直布局" />
```

## 组件属性

### Loading 基础组件
- `size`: 'sm' | 'md' | 'lg' - 图标大小
- `text`: string - 显示文本
- `direction`: 'horizontal' | 'vertical' - 布局方向
- `className`: string - 自定义样式类
- `textClassName`: string - 文本样式类
- `iconClassName`: string - 图标样式类
- `centered`: boolean - 是否居中显示
- `icon`: ReactNode - 自定义图标

### 预设组件
- `PageLoading`: 页面级加载，默认大尺寸、垂直布局
- `ButtonLoading`: 按钮内加载，默认小尺寸、水平布局
- `CardLoading`: 卡片内加载，默认中等尺寸、垂直布局、居中显示
- `InlineLoading`: 内联加载，默认小尺寸、水平布局

## 迁移指南

### 从旧的 loading 实现迁移

**旧代码：**
```tsx
{isLoading && (
  <div className="flex items-center justify-center">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
    <span className="ml-2">加载中...</span>
  </div>
)}
```

**新代码：**
```tsx
{isLoading && <InlineLoading text="加载中..." />}
```

**页面级 loading 迁移：**

**旧代码：**
```tsx
if (isLoading) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    </div>
  )
}
```

**新代码：**
```tsx
if (isLoading) {
  return (
    <div className="container mx-auto px-4 py-8">
      <PageLoading text="加载中..." />
    </div>
  )
}
