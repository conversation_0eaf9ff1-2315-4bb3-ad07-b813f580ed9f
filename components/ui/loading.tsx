import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

export interface LoadingProps {
  /**
   * Loading 的大小
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * 是否显示文本
   */
  text?: string
  /**
   * 布局方向
   */
  direction?: 'horizontal' | 'vertical'
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 文本类名
   */
  textClassName?: string
  /**
   * 图标类名
   */
  iconClassName?: string
  /**
   * 是否居中显示
   */
  centered?: boolean
  /**
   * 自定义图标
   */
  icon?: React.ReactNode
}

const sizeMap = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6', 
  lg: 'h-8 w-8'
}

const textSizeMap = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg'
}

export function Loading({
  size = 'md',
  text,
  direction = 'horizontal',
  className,
  textClassName,
  iconClassName,
  centered = false,
  icon
}: LoadingProps) {
  const LoadingIcon = icon || <Loader2 className={cn('animate-spin', sizeMap[size], iconClassName)} />
  
  const content = (
    <div
      className={cn(
        'flex items-center',
        direction === 'vertical' ? 'flex-col space-y-2' : 'space-x-2',
        className
      )}
    >
      {LoadingIcon}
      {text && (
        <span className={cn('text-muted-foreground', textSizeMap[size], textClassName)}>
          {text}
        </span>
      )}
    </div>
  )

  if (centered) {
    return (
      <div className="flex items-center justify-center">
        {content}
      </div>
    )
  }

  return content
}

/**
 * 页面级别的 Loading 组件
 */
export function PageLoading({
  text = '加载中...',
  className,
  ...props
}: Omit<LoadingProps, 'centered'> & { className?: string }) {
  return (
    <div className={cn('flex items-center justify-center min-h-[400px]', className)}>
      <Loading
        size="lg"
        text={text}
        direction="vertical"
        {...props}
      />
    </div>
  )
}

/**
 * 按钮内的 Loading 组件
 */
export function ButtonLoading({
  text,
  size = 'sm',
  ...props
}: Omit<LoadingProps, 'direction' | 'centered'>) {
  return (
    <Loading
      size={size}
      text={text}
      direction="horizontal"
      {...props}
    />
  )
}

/**
 * 卡片内的 Loading 组件
 */
export function CardLoading({
  text = '加载中...',
  className,
  ...props
}: Omit<LoadingProps, 'centered'>) {
  return (
    <div className={cn('flex items-center justify-center py-8', className)}>
      <Loading
        text={text}
        direction="vertical"
        centered
        {...props}
      />
    </div>
  )
}

/**
 * 内联 Loading 组件
 */
export function InlineLoading({
  text,
  size = 'sm',
  ...props
}: Omit<LoadingProps, 'direction' | 'centered'>) {
  return (
    <Loading
      size={size}
      text={text}
      direction="horizontal"
      {...props}
    />
  )
}
