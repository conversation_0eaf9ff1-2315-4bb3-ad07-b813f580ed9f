'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface HighlightedTextareaProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  rows?: number;
  className?: string;
  id?: string;
}

export function HighlightedTextarea({
  value,
  onChange,
  onBlur,
  placeholder,
  rows = 8,
  className,
  id,
}: HighlightedTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const highlightRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 同步滚动
  const handleScroll = () => {
    if (textareaRef.current && highlightRef.current) {
      highlightRef.current.scrollTop = textareaRef.current.scrollTop;
      highlightRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  // 高亮处理函数
  const getHighlightedText = (text: string) => {
    if (!text) return '&nbsp;'; // 确保有内容以保持高度
    
    // 使用正则表达式匹配 {{ xxx }} 模式
    const regex = /(\{\{[^}]*\}\})/g;
    
    // 转义HTML字符
    const escapeHtml = (str: string) => {
      return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/\n/g, '<br>')
        .replace(/ /g, '&nbsp;');
    };
    
    const parts = text.split(regex);
    
    return parts.map((part) => {
      if (regex.test(part)) {
        return `<span style="background-color: #dbeafe; color: #1e40af; padding: 0; border-radius: 2px;">${escapeHtml(part)}</span>`;
      }
      // 非高亮部分使用完全透明的span包裹，确保不会显示
      return `<span style="color: transparent;">${escapeHtml(part)}</span>`;
    }).join('');
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  // 处理焦点
  const handleFocus = () => {
    // 可以在这里添加焦点处理逻辑
  };

  const handleBlur = () => {
    onBlur?.();
  };

  // 同步样式
  useEffect(() => {
    if (textareaRef.current && highlightRef.current) {
      const textarea = textareaRef.current;
      const highlight = highlightRef.current;
      
      // 获取计算后的样式
      const computedStyle = window.getComputedStyle(textarea);
      
      // 同步所有相关样式，确保完全一致
      highlight.style.fontSize = computedStyle.fontSize;
      highlight.style.fontFamily = computedStyle.fontFamily;
      highlight.style.lineHeight = computedStyle.lineHeight;
      highlight.style.padding = computedStyle.padding;
      highlight.style.paddingTop = computedStyle.paddingTop;
      highlight.style.paddingRight = computedStyle.paddingRight;
      highlight.style.paddingBottom = computedStyle.paddingBottom;
      highlight.style.paddingLeft = computedStyle.paddingLeft;
      highlight.style.margin = computedStyle.margin;
      highlight.style.border = 'transparent';
      highlight.style.borderWidth = computedStyle.borderWidth;
      highlight.style.borderStyle = computedStyle.borderStyle;
      highlight.style.boxSizing = computedStyle.boxSizing;
      highlight.style.wordWrap = computedStyle.wordWrap;
      highlight.style.whiteSpace = 'pre-wrap';
      highlight.style.letterSpacing = computedStyle.letterSpacing;
      highlight.style.wordSpacing = computedStyle.wordSpacing;
      highlight.style.textIndent = computedStyle.textIndent;
      highlight.style.width = computedStyle.width;
      highlight.style.height = computedStyle.height;
    }
  }, [value]); // 当值改变时重新同步

  return (
    <div ref={containerRef} className="relative">
      {/* 高亮背景层 */}
      <div
        ref={highlightRef}
        className={cn(
          "absolute inset-0 pointer-events-none overflow-hidden",
          "whitespace-pre-wrap break-words",
          className
        )}
        style={{
          zIndex: 1,
          color: 'transparent',
          backgroundColor: 'transparent',
          caretColor: 'transparent',
          resize: 'none',
          border: 'none',
          outline: 'none',
          textShadow: 'none',
          WebkitTextStroke: 'none',
        }}
        dangerouslySetInnerHTML={{
          __html: getHighlightedText(value)
        }}
      />
      
      {/* 实际的 textarea */}
      <textarea
        ref={textareaRef}
        id={id}
        value={value}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onScroll={handleScroll}
        placeholder={placeholder}
        rows={rows}
        className={cn(
          "relative resize-none outline-none",
          "flex min-h-[80px] w-full rounded-md border border-input px-3 py-2 text-sm",
          "ring-offset-background placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        style={{
          zIndex: 2,
          backgroundColor: 'transparent',
          color: 'inherit',
        }}
      />
    </div>
  );
}
