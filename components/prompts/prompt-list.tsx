'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Edit, Trash2, Eye, Plus, Globe, Lock } from 'lucide-react';
import type { PromptWithParameters } from '@/types';
import { deletePromptAPI } from '@/lib/api';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface PromptListProps {
  prompts: PromptWithParameters[];
  onPromptDeleted: () => void;
}

export function PromptList({ prompts, onPromptDeleted }: PromptListProps) {
  const t = useTranslations();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [promptToDelete, setPromptToDelete] = useState<PromptWithParameters | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (prompt: PromptWithParameters) => {
    setPromptToDelete(prompt);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!promptToDelete) return;

    setIsDeleting(true);
    try {
      await deletePromptAPI(promptToDelete.id);
      toast.success(t('prompts.delete.success'));
      onPromptDeleted();
      setDeleteDialogOpen(false);
      setPromptToDelete(null);
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast.error(t('prompts.delete.failed'));
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  if (prompts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <Plus className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('prompts.noPrompts')}</h3>
        <p className="text-gray-500 mb-6">{t('prompts.noPromptsDescription')}</p>
        <Link href="/prompts/create">
          <Button>
            <Plus className="w-4 h-4" />
            {t('prompts.create')}
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[240px] pl-6">{t('prompts.table.name')}</TableHead>
              <TableHead className="hidden md:table-cell w-full">{t('prompts.table.description')}</TableHead>
              <TableHead className="w-[80px] hidden sm:table-cell text-center">{t('prompts.table.parameters')}</TableHead>
              <TableHead className="w-[80px] hidden lg:table-cell text-center">{t('prompts.table.isPublic')}</TableHead>
              <TableHead className="w-[120px] hidden sm:table-cell">{t('prompts.table.updatedAt')}</TableHead>
              <TableHead className="w-[100px] text-center">{t('prompts.table.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {prompts.map((prompt) => (
              <TableRow key={prompt.id} className="hover:bg-muted/50">
                <TableCell className="font-medium">
                  <div className="max-w-[240px] truncate" title={prompt.name}>
                    <Link href={`/prompts/detail/${prompt.id}`}>
                      <Button variant="link">
                        {prompt.name}
                      </Button>
                    </Link>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell w-full">
                  <div className="w-full truncate text-muted-foreground" title={prompt.description || ''}>
                    {prompt.description || t('prompts.table.noDescription')}
                  </div>
                </TableCell>
                <TableCell className="text-center hidden sm:table-cell">
                  <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                    {prompt.parameters.length}
                  </span>
                </TableCell>
                <TableCell className="text-center hidden lg:table-cell">
                  <div className="flex items-center justify-center">
                    {prompt.isPublic ? (
                      <div className="flex items-center space-x-1 text-green-600">
                        <Globe className="w-4 h-4" />
                        <span className="text-xs">{t('prompts.status.public')}</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 text-gray-500">
                        <Lock className="w-4 h-4" />
                        <span className="text-xs">{t('prompts.status.private')}</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="hidden sm:table-cell text-sm text-muted-foreground">
                  {formatDate(prompt.updatedAt)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center justify-center space-x-1">
                    <Link href={`/prompts/edit/${prompt.id}`}>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title={t('prompts.actions.edit')}>
                        <Edit className="w-4 h-4" />
                      </Button>
                    </Link>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleDeleteClick(prompt)}
                      title={t('prompts.actions.delete')}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('prompts.delete.confirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('prompts.delete.confirmDescription', { name: promptToDelete?.name || '' })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              {t('prompts.delete.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? t('prompts.delete.deleting') : t('prompts.delete.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
