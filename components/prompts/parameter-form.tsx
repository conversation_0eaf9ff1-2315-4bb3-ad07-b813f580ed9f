'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Trash2, Plus } from 'lucide-react';
import type { ParameterFormData } from '@/types';
import type { ValidationError } from '@/lib/validations';
import { getFieldError, hasFieldError } from '@/lib/validations';
import { useTranslations } from 'next-intl';

interface ParameterFormProps {
  parameters: ParameterFormData[];
  onChange: (parameters: ParameterFormData[]) => void;
  errors: ValidationError[];
  onFieldBlur: (fieldName: string) => void;
}

export function ParameterForm({ parameters, onChange, errors, onFieldBlur }: ParameterFormProps) {
  const t = useTranslations();

  const addParameter = () => {
    onChange([
      ...parameters,
      {
        name: '',
        description: '',
        required: false,
      },
    ]);
  };

  const removeParameter = (index: number) => {
    onChange(parameters.filter((_, i) => i !== index));
  };

  const updateParameter = (index: number, field: keyof ParameterFormData, value: any) => {
    const updated = parameters.map((param, i) => {
      if (i === index) {
        return { ...param, [field]: value };
      }
      return param;
    });
    onChange(updated);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">{t('prompts.form.parameters')}</Label>
        <Button type="button" variant="outline" size="sm" onClick={addParameter}>
          <Plus className="w-4 h-4 mr-2" />
          {t('prompts.form.addParameter')}
        </Button>
      </div>

      {parameters.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-gray-500">
            <Plus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>{t('prompts.form.noParameters')}</p>
            <p className="text-sm">{t('prompts.form.noParametersDescription')}</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {parameters.map((parameter, index) => (
            <div
              key={index}
              className="p-4 border border-gray-200 rounded-lg space-y-4 bg-gray-50"
            >
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm text-gray-700">
                  {t('prompts.form.parameterNumber', { number: index + 1 })}
                </h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeParameter(index)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`param-name-${index}`}>
                    {t('prompts.form.parameterName')} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id={`param-name-${index}`}
                    value={parameter.name}
                    onChange={(e) => updateParameter(index, 'name', e.target.value)}
                    onBlur={() => onFieldBlur(`parameters.${index}.name`)}
                    placeholder={t('prompts.form.parameterNamePlaceholder')}
                    className={
                      hasFieldError(errors, `parameters.${index}.name`)
                        ? 'border-red-500 focus:border-red-500'
                        : ''
                    }
                  />
                  {getFieldError(errors, `parameters.${index}.name`) && (
                    <p className="text-sm text-red-600">
                      {getFieldError(errors, `parameters.${index}.name`)}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`param-required-${index}`}
                      checked={parameter.required}
                      onChange={(e) => updateParameter(index, 'required', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor={`param-required-${index}`} className="text-sm">
                      {t('prompts.form.required')}
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor={`param-description-${index}`}>{t('prompts.form.parameterDescription')}</Label>
                <Textarea
                  id={`param-description-${index}`}
                  value={parameter.description || ''}
                  onChange={(e) => updateParameter(index, 'description', e.target.value)}
                  onBlur={() => onFieldBlur(`parameters.${index}.description`)}
                  placeholder={t('prompts.form.parameterDescriptionPlaceholder')}
                  rows={2}
                  className={
                    hasFieldError(errors, `parameters.${index}.description`)
                      ? 'border-red-500 focus:border-red-500'
                      : ''
                  }
                />
                {getFieldError(errors, `parameters.${index}.description`) && (
                  <p className="text-sm text-red-600">
                    {getFieldError(errors, `parameters.${index}.description`)}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {parameters.length > 0 && (
        <div className="text-sm text-gray-600">
          <p>{t('prompts.form.parameterNameRule')}</p>
        </div>
      )}
    </div>
  );
}
