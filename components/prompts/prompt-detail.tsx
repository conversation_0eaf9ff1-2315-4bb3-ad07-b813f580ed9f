'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { ArrowLeft, Edit, Trash2, Copy, Check, Globe, Lock } from 'lucide-react';
import type { PromptWithParameters } from '@/types';
import { deletePromptAPI } from '@/lib/api';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface PromptDetailProps {
  prompt: PromptWithParameters;
}

export function PromptDetail({ prompt }: PromptDetailProps) {
  const t = useTranslations();
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [copiedContent, setCopiedContent] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      await deletePromptAPI(prompt.id);
      toast.success(t('prompts.delete.success'));
      router.push('/prompts');
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast.error(t('prompts.delete.failed'));
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      setCopiedContent(true);
      toast.success(t('ui.copied'));
      setTimeout(() => setCopiedContent(false), 2000);
    } catch (error) {
      toast.error(t('common.operationFailed'));
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Asia/Shanghai', // 明确指定中国时区
    }).format(date); // 移除重复的 new Date() 调用
  };

  return (
    <>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="w-4 h-4" />
              {t('ui.back')}
            </Button>
            <h1 className="text-2xl font-bold">{prompt.name}</h1>
          </div>

          <div className="flex space-x-2">
            <Link href={`/prompts/edit/${prompt.id}`}>
              <Button variant="outline">
                <Edit className="w-4 h-4" />
                {t('ui.edit')}
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={handleDeleteClick}
            >
              <Trash2 className="w-4 h-4" />
              {t('ui.delete')}
            </Button>
          </div>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('ui.basicInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700 mb-1">{t('ui.name')}</h3>
                <p className="text-gray-900">{prompt.name}</p>
              </div>

              {prompt.description && (
                <div>
                  <h3 className="font-medium text-gray-700 mb-1">{t('ui.description')}</h3>
                  <p className="text-gray-900">{prompt.description}</p>
                </div>
              )}

              <div>
                <h3 className="font-medium text-gray-700 mb-1">{t('prompts.table.isPublic')}</h3>
                <div className="flex items-center space-x-2">
                  {prompt.isPublic ? (
                    <>
                      <Globe className="w-4 h-4 text-green-600" />
                      <span className="text-green-600">{t('prompts.status.public')}</span>
                    </>
                  ) : (
                    <>
                      <Lock className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-500">{t('prompts.status.private')}</span>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t('ui.content')}</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyContent}
                  className="flex items-center space-x-2"
                >
                  {copiedContent ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                  <span>{copiedContent ? t('ui.copied') : t('ui.copy')}</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-900 font-mono">
                  {prompt.content}
                </pre>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('ui.parameterList')} ({prompt.parameters.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {prompt.parameters.length === 0 ? (
                <p className="text-gray-500 text-center py-4">{t('ui.noParameters')}</p>
              ) : (
                <div className="space-y-4">
                  {prompt.parameters.map((parameter) => (
                    <div
                      key={parameter.id}
                      className="p-4 border border-gray-200 rounded-lg bg-gray-50"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-gray-900">
                          {parameter.name}
                          {parameter.required && (
                            <span className="ml-1 text-red-500">*</span>
                          )}
                        </h4>
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            parameter.required
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {parameter.required ? t('ui.required') : t('ui.optional')}
                        </span>
                      </div>
                      {parameter.description && (
                        <p className="text-gray-600 text-sm">
                          {parameter.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 时间戳信息 - 位于页面底部 */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
            <div>
              <span className="font-medium">{t('ui.createdAt')}：</span>
              <span>{formatDate(prompt.createdAt)}</span>
            </div>
            <div>
              <span className="font-medium">{t('ui.updatedAt')}：</span>
              <span>{formatDate(prompt.updatedAt)}</span>
            </div>
          </div>
        </div>
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('prompts.delete.confirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('prompts.delete.confirmDescription', { name: prompt.name })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              {t('prompts.delete.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? t('prompts.delete.deleting') : t('prompts.delete.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
