'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { HighlightedTextarea } from '@/components/ui/highlighted-textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { ParameterForm } from './parameter-form';
import { ArrowLeft } from 'lucide-react';
import type { PromptFormData, PromptWithParameters } from '@/types';
import { validatePromptForm, getFieldError, hasFieldError } from '@/lib/validations';
import { createPromptAPI, updatePromptAPI } from '@/lib/api';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface PromptFormProps {
  initialData?: PromptWithParameters;
  mode: 'create' | 'edit';
}

export function PromptForm({ initialData, mode }: PromptFormProps) {
  const t = useTranslations();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<PromptFormData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    content: initialData?.content || '',
    isPublic: initialData?.isPublic || false,
    parameters: initialData?.parameters.map(p => ({
      name: p.name,
      description: p.description || '',
      required: p.required,
    })) || [],
  });

  // 跟踪哪些字段已经失去焦点
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // 只对已失去焦点的字段进行校验
  const validation = validatePromptForm(formData);
  const filteredValidation = {
    isValid: validation.isValid,
    errors: validation.errors.filter(error => touchedFields.has(error.field))
  };

  // 处理字段失去焦点
  const handleFieldBlur = (fieldName: string) => {
    setTouchedFields(prev => new Set(prev).add(fieldName));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 提交时使用完整的校验，不只是已失去焦点的字段
    if (!validation.isValid) {
      // 标记所有字段为已触摸，以显示所有错误
      const allFields = new Set<string>();
      validation.errors.forEach(error => allFields.add(error.field));
      setTouchedFields(allFields);
      toast.error(t('common.operationFailed'));
      return;
    }

    setIsSubmitting(true);

    try {
      if (mode === 'create') {
        await createPromptAPI(formData);
        toast.success(t('prompts.create') + ' ' + t('common.operationSuccess', { defaultValue: '成功' }));
      } else if (initialData) {
        await updatePromptAPI(initialData.id, formData);
        toast.success(t('prompts.update', { defaultValue: 'Prompt 更新' }) + ' ' + t('common.operationSuccess', { defaultValue: '成功' }));
      }

      router.push('/prompts');
    } catch (error) {
      console.error('Error saving prompt:', error);
      const errorMessage = error instanceof Error ? error.message : t('common.unknownError', { defaultValue: '未知错误' });
      toast.error(`${mode === 'create' ? t('prompts.create') : t('prompts.update', { defaultValue: '更新' })} ${t('common.operationFailed')}: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="w-4 h-4" />
          {t('common.back', { defaultValue: '返回' })}
        </Button>
        <h1 className="text-2xl font-bold">
          {mode === 'create' ? t('prompts.create') : t('prompts.edit', { defaultValue: '编辑 Prompt' })}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('prompts.form.basicInfo', { defaultValue: '基本信息' })}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                {t('prompts.table.name')} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                onBlur={() => handleFieldBlur('name')}
                placeholder={t('prompts.form.namePlaceholder', { defaultValue: '请输入 Prompt 名称' })}
                className={
                  hasFieldError(filteredValidation.errors, 'name')
                    ? 'border-red-500 focus:border-red-500'
                    : ''
                }
              />
              {getFieldError(filteredValidation.errors, 'name') && (
                <p className="text-sm text-red-600">
                  {getFieldError(filteredValidation.errors, 'name')}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('prompts.table.description')}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                onBlur={() => handleFieldBlur('description')}
                placeholder={t('prompts.form.descriptionPlaceholder', { defaultValue: '请输入 Prompt 描述（可选）' })}
                rows={3}
                className={
                  hasFieldError(filteredValidation.errors, 'description')
                    ? 'border-red-500 focus:border-red-500'
                    : ''
                }
              />
              {getFieldError(filteredValidation.errors, 'description') && (
                <p className="text-sm text-red-600">
                  {getFieldError(filteredValidation.errors, 'description')}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">
                {t('prompts.form.content', { defaultValue: 'Prompt 内容' })} <span className="text-red-500">*</span>
              </Label>
              <p className='text-sm text-gray-500'>{t('prompts.form.contentTip', { defaultValue: '通过 {{xxx}} 使用底部设置的参数，调用时会动态替换为调用时填写的参数值' })}</p>
              <HighlightedTextarea
                id="content"
                value={formData.content}
                onChange={(value) => setFormData({ ...formData, content: value })}
                onBlur={() => handleFieldBlur('content')}
                placeholder={t('prompts.form.contentPlaceholder', { defaultValue: '请输入 Prompt 内容' })}
                rows={8}
                className={
                  hasFieldError(filteredValidation.errors, 'content')
                    ? 'border-red-500 focus:border-red-500'
                    : ''
                }
              />
              {getFieldError(filteredValidation.errors, 'content') && (
                <p className="text-sm text-red-600">
                  {getFieldError(filteredValidation.errors, 'content')}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked: boolean) => setFormData({ ...formData, isPublic: checked })}
                />
                <Label htmlFor="isPublic" className="text-sm font-medium">
                  {t('prompts.form.isPublic', { defaultValue: '公开' })}
                </Label>
              </div>
              <p className="text-sm text-gray-500">
                {t('prompts.form.isPublicDescription', { defaultValue: '公开的 Prompt 可以被其他用户查看和使用' })}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('prompts.form.parameters')}</CardTitle>
          </CardHeader>
          <CardContent>
            <ParameterForm
              parameters={formData.parameters}
              onChange={(parameters) => setFormData({ ...formData, parameters })}
              errors={filteredValidation.errors}
              onFieldBlur={handleFieldBlur}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={handleBack}>
            {t('common.cancel', { defaultValue: '取消' })}
          </Button>
          <Button type="submit" disabled={isSubmitting || !validation.isValid}>
            {isSubmitting
              ? (mode === 'create' ? t('prompts.creating', { defaultValue: '创建中...' }) : t('prompts.saving', { defaultValue: '保存中...' }))
              : (mode === 'create' ? t('prompts.create') : t('prompts.save', { defaultValue: '保存' }))
            }
          </Button>
        </div>
      </form>
    </div>
  );
}
