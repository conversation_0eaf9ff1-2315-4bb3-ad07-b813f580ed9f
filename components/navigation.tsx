"use client"

import Link from "next/link"
import Image from "next/image"
import { useRouter, usePathname } from "next/navigation"
import { useSession } from "@/hooks/use-session"
import { Button } from "@/components/ui/button"
import { LanguageSwitcher } from "@/components/language-switcher"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Settings, User, LogOut } from "lucide-react"
import { clsx } from "clsx"
import { useTranslations } from 'next-intl'

export function Navigation() {
  const { user, isAuthenticated, signOut } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const t = useTranslations('navigation')

  const handleSignOut = async () => {
    await signOut()
    router.push("/")
  }

  // 在首页不显示导航栏
  if (pathname === '/' || pathname.startsWith('/admin')) {
    return null
  }

  return (
    <nav className="bg-white z-10" style={{boxShadow: '0 0 0 1px #ededed'}}>
      <div className="mx-auto px-4">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="items-center font-bold text-gray-900 mr-2 hidden md:flex">
              <Image
                src="/logo.png"
                alt="Prompt Repo Logo"
                width={32}
                height={32}
                className="h-8"
              />
              <span className={clsx("font-bold text-gray-900", { 'hidden md:block': isAuthenticated })}>Prompt Repo</span>
            </Link>
            <>

              <Link href="/" className={clsx('md:hidden', { 'block': !isAuthenticated, 'hidden':isAuthenticated })}>
                <Button variant="ghost">
                  {t('home')}
                </Button>
              </Link>

              <Link href="/prompts" className={clsx({ 'hidden md:block': !isAuthenticated })}>
                <Button variant={pathname.startsWith('/prompts') ? "secondary" : "ghost"}>
                  {t('promptManagement')}
                </Button>
              </Link>
              <Link href="/mcp" className={clsx('ml-2', { 'hidden md:block': !isAuthenticated })}>
                <Button variant={pathname.startsWith('/mcp') ? "secondary" : "ghost"}>
                  {t('mcpUsage')}
                </Button>
              </Link>
              <Link href="/chat" className={clsx('ml-2', { 'hidden md:block': !isAuthenticated })}>
                <Button variant={pathname.startsWith('/chat') ? "secondary" : "ghost"}>
                  {t('chat')}
                </Button>
              </Link>

              <Link href="/explore" className="ml-2">
                <Button variant={pathname.startsWith('/explore') ? "secondary" : "ghost"}>
                  {t('explore')}
                </Button>
              </Link>
            </>
          </div>
          <div className="flex items-center space-x-2">
            <LanguageSwitcher />
            {isAuthenticated ? (
              <>
                <Link href="https://jsj.top/f/LpedUQ" target="_blank" className="hidden lg:block">
                  <Button variant="ghost">
                    {t('feedback')}
                  </Button>
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-9 w-9">
                      <Settings className="h-4 w-4" />
                      <span className="sr-only">设置</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="flex items-center gap-2 w-full">
                        <User className="h-4 w-4" />
                        个人资料
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSignOut} className="flex items-center gap-2">
                      <LogOut className="h-4 w-4" />
                      {t('logout')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost">{t('login')}</Button>
                </Link>
                <Link href="/signup">
                  <Button>{t('signup')}</Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
