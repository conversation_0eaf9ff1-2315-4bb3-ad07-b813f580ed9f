"use client"

import { useState } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ChangePasswordForm } from './change-password-form'
import { EditNicknameModal } from './edit-nickname-modal'
import {
  User,
  Mail,
  Calendar,
  Shield,
  Key,
  Github,
  Chrome,
  CheckCircle,
  XCircle
} from 'lucide-react'
import type { UserProfile, LoginMethod } from '@/types/profile'

interface ProfilePageProps {
  profile: UserProfile
}

export function ProfilePage({ profile }: ProfilePageProps) {
  const [currentNickname, setCurrentNickname] = useState(profile.name)

  const handleNicknameUpdate = (newNickname: string) => {
    setCurrentNickname(newNickname)
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'github':
        return <Github className="h-4 w-4" />
      case 'google':
        return <Chrome className="h-4 w-4" />
      default:
        return <Key className="h-4 w-4" />
    }
  }

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'github':
        return 'GitHub'
      case 'google':
        return 'Google'
      case 'facebook':
        return 'Facebook'
      case 'twitter':
        return 'Twitter'
      case 'discord':
        return 'Discord'
      case 'microsoft':
        return 'Microsoft'
      default:
        return provider
    }
  }

  const getLoginMethodDescription = (loginMethods: LoginMethod[], hasPassword: boolean): string => {
    const methods: string[] = []

    if (hasPassword) {
      methods.push('邮箱密码')
    }

    loginMethods.forEach(method => {
      switch (method.provider) {
        case 'github':
          methods.push('GitHub')
          break
        case 'google':
          methods.push('Google')
          break
        case 'facebook':
          methods.push('Facebook')
          break
        case 'twitter':
          methods.push('Twitter')
          break
        case 'discord':
          methods.push('Discord')
          break
        case 'microsoft':
          methods.push('Microsoft')
          break
        default:
          // 只有真正的OAuth提供商才会到这里，因为我们已经在服务器端过滤了
          break
      }
    })

    if (methods.length === 0) {
      return '邮箱'
    }

    return methods.join('、')
  }

  const loginMethodDesc = getLoginMethodDescription(profile.loginMethods, profile.hasPassword)
  const canModifyPassword = profile.hasPassword || profile.hasCredentialAccount

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">个人资料</h1>
        </div>

        {/* 基本信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              基本信息
            </CardTitle>
            <CardDescription>
              您的账户基本信息
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 用户头像和基本信息 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                {profile.image ? (
                  <Image
                    src={profile.image}
                    alt={profile.name}
                    width={80}
                    height={80}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex-1 space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">用户昵称</label>
                  <div className="flex items-center gap-2">
                    <p className="text-lg font-medium text-gray-900">{currentNickname}</p>
                    <EditNicknameModal
                      currentNickname={currentNickname}
                      onNicknameUpdate={handleNicknameUpdate}
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-1">
                    <Mail className="h-4 w-4" />
                    邮箱地址
                  </label>
                  <div className="flex items-center gap-2">
                    <p className="text-gray-900">{profile.email}</p>
                    {profile.emailVerified ? (
                      <Badge variant="secondary" className="text-green-700 bg-green-50">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        已验证
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="text-gray-700 bg-gray-100">
                        未验证
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 账户信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500 flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  注册时间
                </label>
                <p className="text-gray-900">
                  {new Date(profile.createdAt).toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 flex items-center gap-1">
                  <Shield className="h-4 w-4" />
                  登录方式
                </label>
                <p className="text-gray-900">{loginMethodDesc}</p>
              </div>
            </div>

            {/* 登录方式详情 - 只显示第三方OAuth账户 */}
            {profile.loginMethods.length > 0 && (
              <>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-gray-500 mb-3 block">
                    关联的第三方账户
                  </label>
                  <div className="space-y-2">
                    {profile.loginMethods.map((method, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        {getProviderIcon(method.provider)}
                        <span className="font-medium">{getProviderName(method.provider)}</span>
                        <Badge variant="outline">已关联</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* 密码修改卡片 - 对邮箱密码用户和credential认证用户显示 */}
        {canModifyPassword && (
          <ChangePasswordForm />
        )}
      </div>
    </div>
  )
}
