"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Edit2, Loader2 } from 'lucide-react'
import { toast } from 'sonner'

interface EditNicknameModalProps {
  currentNickname: string
  onNicknameUpdate: (newNickname: string) => void
}

export function EditNicknameModal({ currentNickname, onNicknameUpdate }: EditNicknameModalProps) {
  const [open, setOpen] = useState(false)
  const [nickname, setNickname] = useState(currentNickname)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // 前端验证
    const trimmedNickname = nickname.trim()
    if (!trimmedNickname) {
      setError('昵称不能为空')
      return
    }

    if (trimmedNickname.length > 50) {
      setError('昵称长度不能超过50个字符')
      return
    }

    if (trimmedNickname === currentNickname) {
      setOpen(false)
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/profile/nickname', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nickname: trimmedNickname }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '更新失败')
      }

      // 成功更新
      onNicknameUpdate(trimmedNickname)
      setOpen(false)
      toast.success('昵称更新成功')
    } catch (error) {
      console.error('更新昵称失败:', error)
      setError(error instanceof Error ? error.message : '更新失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setNickname(currentNickname)
    setError('')
    setOpen(false)
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      handleCancel()
    } else {
      setOpen(newOpen)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-gray-400 hover:text-gray-600"
          title="编辑昵称"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] sm:max-w-md">
        <DialogHeader>
          <DialogTitle>编辑昵称</DialogTitle>
          <DialogDescription>
            修改您的显示昵称
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="nickname">昵称</Label>
            <Input
              id="nickname"
              value={nickname}
              onChange={(e) => setNickname(e.target.value)}
              placeholder="请输入昵称"
              maxLength={50}
              disabled={isLoading}
              className={error ? 'border-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error}</p>
            )}
            <p className="text-xs text-gray-500">
              {nickname.length}/50 字符
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !nickname.trim() || nickname.trim() === currentNickname}
            >
              {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
              保存
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
