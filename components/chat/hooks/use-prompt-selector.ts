import { useState, useCallback, useEffect } from 'react';
import type { PromptWithParameters } from '@/types';
import type {
  PromptSelectorState,
  UsePromptSelectorOptions,
  PromptTriggerMode,
  PromptSelectHandler
} from '../types';

export function usePromptSelector({
  onPromptSelect,
  onStateChange
}: UsePromptSelectorOptions = {}) {
  // 提示词选择器状态
  const [state, setState] = useState<PromptSelectorState>({
    isOpen: false,
    prompts: [],
    isLoading: false,
    selectedIndex: 0,
    triggerMode: null,
    triggerPosition: null
  });

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<PromptSelectorState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates };
      onStateChange?.(newState);
      return newState;
    });
  }, [onStateChange]);

  // 获取提示词数据
  const fetchPrompts = useCallback(async () => {
    if (state.isLoading) return;

    updateState({ isLoading: true });
    
    try {
      const response = await fetch('/api/prompts');
      if (response.ok) {
        const data = await response.json();
        updateState({ 
          prompts: data,
          isLoading: false 
        });
      } else {
        console.error('Failed to fetch prompts');
        updateState({ 
          prompts: [],
          isLoading: false 
        });
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
      updateState({ 
        prompts: [],
        isLoading: false 
      });
    }
  }, [state.isLoading, updateState]);

  // 打开选择器
  const openSelector = useCallback((
    triggerMode: PromptTriggerMode,
    triggerPosition?: number
  ) => {
    updateState({
      isOpen: true,
      triggerMode,
      triggerPosition: triggerPosition ?? null,
      selectedIndex: 0
    });

    // 如果还没有加载过提示词，则加载
    if (state.prompts.length === 0 && !state.isLoading) {
      fetchPrompts();
    }
  }, [state.prompts.length, state.isLoading, fetchPrompts, updateState]);

  // 关闭选择器
  const closeSelector = useCallback(() => {
    updateState({
      isOpen: false,
      triggerMode: null,
      triggerPosition: null,
      selectedIndex: 0
    });
  }, [updateState]);

  // 选择提示词
  const selectPrompt = useCallback((prompt: PromptWithParameters) => {
    onPromptSelect?.(prompt);
    closeSelector();
  }, [onPromptSelect, closeSelector]);

  // 键盘导航
  const handleKeyboardNavigation = useCallback((direction: 'up' | 'down' | 'enter' | 'escape') => {
    if (!state.isOpen || state.prompts.length === 0) return;

    switch (direction) {
      case 'up':
        updateState({
          selectedIndex: state.selectedIndex > 0 
            ? state.selectedIndex - 1 
            : state.prompts.length - 1
        });
        break;
      
      case 'down':
        updateState({
          selectedIndex: state.selectedIndex < state.prompts.length - 1 
            ? state.selectedIndex + 1 
            : 0
        });
        break;
      
      case 'enter':
        if (state.prompts[state.selectedIndex]) {
          selectPrompt(state.prompts[state.selectedIndex]);
        }
        break;
      
      case 'escape':
        closeSelector();
        break;
    }
  }, [state.isOpen, state.prompts, state.selectedIndex, updateState, selectPrompt, closeSelector]);

  // 检测斜杠触发
  const checkSlashTrigger = useCallback((content: string) => {
    if (content.endsWith('/')) {
      const cursorPosition = content.length - 1;
      openSelector('slash', cursorPosition);
      return true;
    } else if (state.isOpen && state.triggerMode === 'slash' && state.triggerPosition !== null) {
      // 检查斜杠是否还存在
      if (state.triggerPosition >= content.length || content[state.triggerPosition] !== '/') {
        closeSelector();
        return false;
      }
    }
    return false;
  }, [state.isOpen, state.triggerMode, state.triggerPosition, openSelector, closeSelector]);

  return {
    // 状态
    state,
    
    // 操作方法
    openSelector,
    closeSelector,
    selectPrompt,
    handleKeyboardNavigation,
    checkSlashTrigger,
    fetchPrompts,
    
    // 便捷访问器
    isOpen: state.isOpen,
    prompts: state.prompts,
    isLoading: state.isLoading,
    selectedIndex: state.selectedIndex,
    triggerMode: state.triggerMode,
    triggerPosition: state.triggerPosition
  };
}
