import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import type {
  ParameterInfo,
  ParsedContent,
  UseParameterizedInputOptions
} from '../types';
import type { PromptWithParameters } from '@/types';

export function useParameterizedInput({
  initialValue = '',
  onContentChange,
  onParametersChange,
  onProcessedContentChange,
  currentPrompt
}: UseParameterizedInputOptions) {
  const [content, setContent] = useState(initialValue);
  const [parameters, setParameters] = useState<Record<string, string>>({});
  const [editingParameter, setEditingParameter] = useState<string | null>(null);

  // 获取当前 prompt 的所有参数 - 直接使用 useMemo 优化
  const promptParameters = useMemo(() => {
    if (!currentPrompt) return [];
    return currentPrompt.parameters.map(p => p.name);
  }, [currentPrompt]);

  // 获取未填写的参数列表
  const unfilledParameters = useMemo(() => {
    return promptParameters.filter(paramName =>
      !parameters[paramName] || parameters[paramName].trim() === ''
    );
  }, [promptParameters, parameters]);



  // 所有使用的参数
  const usedParameters = promptParameters;

  // 获取参数信息
  const parameterInfos = useMemo((): ParameterInfo[] => {
    return promptParameters.map(paramName => {
      const isUnfilled = unfilledParameters.includes(paramName);
      const value = parameters[paramName] || '';
      const paramDef = currentPrompt?.parameters.find(p => p.name === paramName);

      return {
        name: paramName,
        value,
        isUnfilled,
        description: paramDef?.description || undefined,
        required: paramDef?.required || false
      };
    });
  }, [promptParameters, unfilledParameters, parameters, currentPrompt]);

  // 解析内容为可渲染的部分（基于 prompt 参数）- 优化依赖
  const parseContent = useCallback((text: string): ParsedContent[] => {
    if (!currentPrompt || promptParameters.length === 0) {
      return [{ text, isParameter: false }];
    }

    // 找到所有参数匹配
    const matches: Array<{
      start: number;
      end: number;
      paramName: string;
      fullMatch: string;
    }> = [];

    promptParameters.forEach(paramName => {
      const parameterRegex = new RegExp(`\\{\\{\\s*${paramName}\\s*\\}\\}`, 'g');
      let match;

      while ((match = parameterRegex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          paramName: paramName,
          fullMatch: match[0]
        });
      }
    });

    // 按位置排序
    matches.sort((a, b) => a.start - b.start);

    // 构建部分数组
    const parts: ParsedContent[] = [];
    let lastEnd = 0;

    matches.forEach(match => {
      // 添加参数前的普通文本
      if (match.start > lastEnd) {
        parts.push({
          text: text.slice(lastEnd, match.start),
          isParameter: false
        });
      }

      // 添加参数部分
      const paramValue = parameters[match.paramName];
      const isUnfilled = !paramValue || paramValue.trim() === '';

      parts.push({
        text: isUnfilled ? match.fullMatch : paramValue, // 未填写显示 {{paramName}}，已填写显示值
        isParameter: true,
        paramName: match.paramName,
        isUnfilled: isUnfilled
      });

      lastEnd = match.end;
    });

    // 添加最后的普通文本
    if (lastEnd < text.length) {
      parts.push({
        text: text.slice(lastEnd),
        isParameter: false
      });
    }

    return parts;
  }, [promptParameters, parameters, currentPrompt]);

  // 获取处理后的内容（替换所有参数）- 使用 useMemo 优化
  const processedContent = useMemo(() => {
    if (promptParameters.length === 0) {
      return content;
    }

    let result = content;

    // 替换所有参数 {{param}} 为实际值（包括已填写和未填写的）
    promptParameters.forEach(param => {
      const value = parameters[param] || '';
      const regex = new RegExp(`\\{\\{\\s*${param}\\s*\\}\\}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }, [content, promptParameters, parameters]);

  // 提供 getProcessedContent 函数以保持向后兼容
  const getProcessedContent = useCallback(() => processedContent, [processedContent]);

  // 更新内容
  const updateContent = useCallback((newContent: string, fromExternal = false) => {
    setContent(newContent);
    // 只有非外部更新时才调用 onContentChange，避免循环更新
    if (!fromExternal) {
      onContentChange?.(newContent);
    }
  }, [onContentChange]);

  // 更新参数值（不自动替换内容，保持 prompt 原始格式）
  const updateParameter = useCallback((paramName: string, value: string) => {
    const newParameters = { ...parameters, [paramName]: value };
    setParameters(newParameters);
    onParametersChange?.(newParameters);
  }, [parameters, onParametersChange]);

  // 开始编辑参数
  const startEditingParameter = useCallback((paramName: string) => {
    setEditingParameter(paramName);
  }, []);

  // 停止编辑参数
  const stopEditingParameter = useCallback(() => {
    setEditingParameter(null);
  }, []);

  // 当 prompt 变化时，初始化参数状态
  useEffect(() => {
    if (currentPrompt) {
      setParameters(prev => {
        const newParams = { ...prev };
        let hasChanges = false;

        // 为新的 prompt 参数初始化空值
        currentPrompt.parameters.forEach(param => {
          if (!(param.name in newParams)) {
            newParams[param.name] = '';
            hasChanges = true;
          }
        });

        // 移除不属于当前 prompt 的参数
        const currentParamNames = currentPrompt.parameters.map(p => p.name);
        Object.keys(newParams).forEach(paramName => {
          if (!currentParamNames.includes(paramName)) {
            delete newParams[paramName];
            hasChanges = true;
          }
        });

        return hasChanges ? newParams : prev;
      });
    } else {
      // 如果没有选中的 prompt，清空参数
      setParameters({});
    }
  }, [currentPrompt]);

  // 通知处理后的内容变化 - 优化依赖
  useEffect(() => {
    onProcessedContentChange?.(processedContent);
  }, [processedContent, onProcessedContentChange]);

  return {
    content,
    parameters,
    unfilledParameters,
    usedParameters,
    parameterInfos,
    editingParameter,
    parseContent,
    getProcessedContent,
    updateContent,
    updateParameter,
    startEditingParameter,
    stopEditingParameter
  };
}
