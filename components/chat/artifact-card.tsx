'use client';

import { cn } from '@/lib/utils';
import { FileCode, Image, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { ArtifactCardProps } from '@/types/artifact';

export function ArtifactCard({ artifact, onClick }: ArtifactCardProps) {
  const getIcon = () => {
    if (artifact.type === 'svg') return <Image className="w-4 h-4" />;
    return <FileCode className="w-4 h-4" />;
  };

  const getTypeLabel = () => {
    if (artifact.type === 'svg') return 'SVG 图片';
    return 'HTML 文件';
  };

  return (
    <div
      className="my-3 p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        {/* 左侧：文件类型信息 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded">
            {getIcon()}
          </div>
          <span className="text-sm font-medium text-gray-900">
            {getTypeLabel()}
          </span>
        </div>

        {/* 右侧：查看按钮 */}
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 text-xs"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          <Eye className="w-3 h-3" />
          查看
        </Button>
      </div>
    </div>
  );
}
