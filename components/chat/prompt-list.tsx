'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { PromptListProps } from './types';

export function PromptList({
  prompts,
  isLoading,
  selectedIndex,
  onPromptSelect,
  className
}: PromptListProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="w-4 h-4 animate-spin mr-2" />
        <span className="text-sm text-gray-500">加载中...</span>
      </div>
    );
  }

  if (prompts.length === 0) {
    return (
      <div className="p-4 text-center text-sm text-gray-500">
        暂无提示词
      </div>
    );
  }

  return (
    <div className={cn("py-2", className)}>
      {prompts.map((prompt, index) => (
        <button
          key={prompt.id}
          data-prompt-index={index}
          className={cn(
            "w-full text-left px-3 py-2 text-sm hover:bg-gray-100 transition-colors border-l-2 border-transparent",
            index === selectedIndex && "bg-blue-50 text-blue-700 border-l-blue-500 font-medium"
          )}
          onClick={() => onPromptSelect(prompt)}
        >
          <div className="font-medium truncate">{prompt.name}</div>
          {prompt.description && (
            <div className="text-xs text-gray-500 truncate mt-1">
              {prompt.description}
            </div>
          )}
        </button>
      ))}
    </div>
  );
}
