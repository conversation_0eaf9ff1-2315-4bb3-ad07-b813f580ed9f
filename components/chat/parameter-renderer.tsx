'use client';

import React, { useRef, useImperativeHandle, forwardRef, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { InlineParameterEditor } from './inline-parameter-editor';
import type {
  ParameterRendererProps,
  ParameterRendererRef,
  InlineParameterEditorRef,
  ParameterInfo
} from './types';

export const ParameterRenderer = forwardRef<ParameterRendererRef, ParameterRendererProps>(({
  parts,
  parameterInfos,
  onParameterValueChange,
  onParameterEditStart,
  onParameterEditEnd,
  className
}, ref) => {
  // 参数编辑器引用映射
  const parameterRefs = useRef<Record<string, InlineParameterEditorRef | null>>({});

  // 创建参数信息映射 - 使用 useMemo 优化
  const parameterInfoMap = useMemo(() => {
    return parameterInfos.reduce((map, info) => {
      map[info.name] = info;
      return map;
    }, {} as Record<string, ParameterInfo>);
  }, [parameterInfos]);

  // 获取参数顺序列表
  const parameterOrder = useMemo(() => {
    return parts
      .filter(part => part.isParameter && part.paramName)
      .map(part => part.paramName!)
      .filter((name, index, arr) => arr.indexOf(name) === index); // 去重，保持第一次出现的顺序
  }, [parts]);

  // 通用的聚焦方法
  const focusParameter = useCallback((paramName: string) => {
    const attemptFocus = () => {
      const paramRef = parameterRefs.current[paramName];
      if (paramRef) {
        try {
          paramRef.focus();
          return true;
        } catch (error) {
          console.warn(`Failed to focus parameter ${paramName}:`, error);
          return false;
        }
      }
      return false;
    };

    // 立即尝试聚焦
    if (!attemptFocus()) {
      // 如果立即聚焦失败，使用 requestAnimationFrame 重试
      requestAnimationFrame(() => {
        if (!attemptFocus()) {
          // 如果还是失败，使用 setTimeout 再次重试
          setTimeout(() => {
            attemptFocus();
          }, 10);
        }
      });
    }
  }, []);

  // 优化 focusFirstParameter 方法
  const focusFirstParameter = useCallback(() => {
    if (parameterOrder.length > 0) {
      focusParameter(parameterOrder[0]);
    } else {
      console.warn('No parameter parts found for focusing');
    }
  }, [parameterOrder, focusParameter]);

  // 聚焦下一个参数
  const focusNextParameter = useCallback((currentParamName: string) => {
    const currentIndex = parameterOrder.indexOf(currentParamName);
    if (currentIndex !== -1) {
      const nextIndex = (currentIndex + 1) % parameterOrder.length; // 循环到第一个
      focusParameter(parameterOrder[nextIndex]);
    }
  }, [parameterOrder, focusParameter]);

  // 聚焦上一个参数
  const focusPrevParameter = useCallback((currentParamName: string) => {
    const currentIndex = parameterOrder.indexOf(currentParamName);
    if (currentIndex !== -1) {
      const prevIndex = currentIndex === 0 ? parameterOrder.length - 1 : currentIndex - 1; // 循环到最后一个
      focusParameter(parameterOrder[prevIndex]);
    }
  }, [parameterOrder, focusParameter]);

  // Tab导航处理
  const handleTabNavigation = useCallback((paramName: string, direction: 'next' | 'prev') => {
    if (direction === 'next') {
      focusNextParameter(paramName);
    } else {
      focusPrevParameter(paramName);
    }
  }, [focusNextParameter, focusPrevParameter]);

  // 暴露 ref 方法
  useImperativeHandle(ref, () => ({
    focusFirstParameter,
    focusNextParameter,
    focusPrevParameter
  }), [focusFirstParameter, focusNextParameter, focusPrevParameter]);

  return (
    <div className={cn("whitespace-pre-wrap break-words", className)}>
      {parts.map((part, index) => {
        if (part.isParameter && part.paramName) {
          const paramInfo = parameterInfoMap[part.paramName];

          return (
            <InlineParameterEditor
              key={index}
              ref={(ref) => {
                if (part.paramName) {
                  parameterRefs.current[part.paramName] = ref;
                }
              }}
              paramName={part.paramName}
              value={paramInfo?.value || ''}
              isUnfilled={part.isUnfilled || false}
              description={paramInfo?.description}
              onValueChange={onParameterValueChange || (() => {})}
              onEditStart={onParameterEditStart}
              onEditEnd={onParameterEditEnd}
              onTabNavigation={handleTabNavigation}
            />
          );
        } else {
          return (
            <span key={index} className="text-gray-900">
              {part.text}
            </span>
          );
        }
      })}
    </div>
  );
});

ParameterRenderer.displayName = 'ParameterRenderer';


