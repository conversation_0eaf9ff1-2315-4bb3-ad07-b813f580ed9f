'use client';

import { useState, useEffect } from 'react';
import { AlertCircle, Loader2 } from 'lucide-react';
import type { ArtifactPreviewProps } from '@/types/artifact';

export function ArtifactPreview({ artifact }: ArtifactPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const isHtml = artifact.type === 'html';
  const isSvg = artifact.type === 'svg';

  // 重置状态当 artifact 改变时
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setErrorMessage('');
  }, [artifact.id]);

  // 处理 iframe 加载完成
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // 处理 iframe 加载错误
  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
    setErrorMessage('预览加载失败');
  };

  // SVG 直接渲染
  if (isSvg) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-white p-4">
        <div 
          className="max-w-full max-h-full"
          dangerouslySetInnerHTML={{ __html: artifact.content }}
          onError={() => {
            setHasError(true);
            setErrorMessage('SVG 渲染失败');
          }}
        />
        {hasError && (
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}
      </div>
    );
  }

  // HTML 使用 iframe 安全渲染
  if (isHtml) {
    // 创建安全的 HTML 内容
    const safeHtmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { 
              margin: 0; 
              padding: 16px; 
              font-family: system-ui, -apple-system, sans-serif;
            }
          </style>
        </head>
        <body>
          ${artifact.content}
        </body>
      </html>
    `;

    const blob = new Blob([safeHtmlContent], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);

    return (
      <div className="w-full h-full relative bg-white">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
            <div className="flex items-center gap-2 text-gray-600">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">正在加载预览...</span>
            </div>
          </div>
        )}
        
        {hasError ? (
          <div className="w-full h-full flex items-center justify-center">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">{errorMessage}</span>
            </div>
          </div>
        ) : (
          <iframe
            src={blobUrl}
            className="w-full h-full border-0"
            sandbox="allow-scripts allow-same-origin"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            title="HTML 预览"
          />
        )}
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="flex items-center gap-2 text-gray-600">
        <AlertCircle className="w-4 h-4" />
        <span className="text-sm">不支持的文件类型</span>
      </div>
    </div>
  );
}
