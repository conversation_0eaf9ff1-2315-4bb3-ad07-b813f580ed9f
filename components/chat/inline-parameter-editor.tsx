'use client';

import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { cn } from '@/lib/utils';
import type {
  InlineParameterEditorProps,
  InlineParameterEditorRef
} from './types';

export const InlineParameterEditor = forwardRef<InlineParameterEditorRef, InlineParameterEditorProps>(({
  paramName,
  value,
  isUnfilled,
  onValueChange,
  onEditStart,
  onEditEnd,
  onTabNavigation,
  className,
  description
}, ref) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);

  // 开始编辑
  const handleStartEditing = useCallback(() => {
    setIsEditing(true);
    setEditValue(value);
    onEditStart?.(paramName);
  }, [value, paramName, onEditStart]);

  // 当进入编辑模式时自动聚焦
  useEffect(() => {
    if (isEditing && inputRef.current) {
      // 使用多重异步策略确保焦点设置成功
      const focusInput = () => {
        if (inputRef.current) {
          try {
            inputRef.current.focus();
            inputRef.current.select();

            // 验证焦点是否设置成功
            if (document.activeElement !== inputRef.current) {
              // 如果焦点设置失败，再次尝试
              requestAnimationFrame(() => {
                if (inputRef.current) {
                  inputRef.current.focus();
                  inputRef.current.select();
                }
              });
            }
          } catch (error) {
            console.warn('Failed to focus parameter input:', error);
          }
        }
      };

      // 立即尝试聚焦
      focusInput();

      // 如果立即聚焦失败，使用 setTimeout 再次尝试
      const timeoutId = setTimeout(focusInput, 0);

      // 最后的保险措施，使用更长的延迟
      const fallbackTimeoutId = setTimeout(focusInput, 10);

      return () => {
        clearTimeout(timeoutId);
        clearTimeout(fallbackTimeoutId);
      };
    }
  }, [isEditing]);

  // 完成编辑
  const handleFinishEditing = useCallback(() => {
    setIsEditing(false);
    onValueChange(paramName, editValue.trim());
    onEditEnd?.();
  }, [paramName, editValue, onValueChange, onEditEnd]);

  // 取消编辑
  const handleCancelEditing = useCallback(() => {
    setIsEditing(false);
    setEditValue(value);
    onEditEnd?.();
  }, [value, onEditEnd]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡，防止触发消息发送
      handleFinishEditing();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡
      handleCancelEditing();
    } else if (e.key === 'Tab') {
      e.preventDefault();
      e.stopPropagation(); // 阻止浏览器默认Tab行为

      // 先完成当前编辑
      handleFinishEditing();

      // 触发Tab导航
      if (onTabNavigation) {
        const direction = e.shiftKey ? 'prev' : 'next';
        onTabNavigation(paramName, direction);
      }
    }
    // 对于其他键盘事件，也阻止冒泡以避免干扰父组件的键盘处理
    e.stopPropagation();
  }, [handleFinishEditing, handleCancelEditing, onTabNavigation, paramName]);

  // 处理失去焦点
  const handleBlur = useCallback(() => {
    handleFinishEditing();
  }, [handleFinishEditing]);

  // 处理点击
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      handleStartEditing();
    }
  }, [isEditing, handleStartEditing]);

  // 暴露 ref 方法
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (isEditing && inputRef.current) {
        // 如果已经在编辑模式，直接聚焦
        const focusWithRetry = () => {
          if (inputRef.current) {
            inputRef.current.focus();
            inputRef.current.select();

            // 验证焦点是否设置成功，如果失败则重试
            if (document.activeElement !== inputRef.current) {
              requestAnimationFrame(() => {
                if (inputRef.current) {
                  inputRef.current.focus();
                  inputRef.current.select();
                }
              });
            }
          }
        };

        focusWithRetry();
      } else {
        // 如果不在编辑模式，先进入编辑模式
        handleStartEditing();
      }
    },
    startEditing: handleStartEditing
  }), [isEditing, handleStartEditing]);

  // 同步外部值变化
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
    }
  }, [value, isEditing]);

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        placeholder={`[${paramName}]`}
        autoFocus // 添加 autoFocus 属性作为额外保障
        className={cn(
          "inline-block px-1 py-0.5 rounded border bg-gray-100 border-blue-500",
          "bg-white text-gray-900 focus:outline-none",
          "min-w-[60px] max-w-[200px]",
          className
        )}
        style={{
          width: `${Math.max(60, editValue.length * 8 + 16)}px`
        }}
      />
    );
  }

  // 显示模式
  const displayText = isUnfilled ? `[${paramName}]` : value;

  return (
    <span
      data-param={paramName}
      onClick={handleClick}
      className={cn(
        "inline-block px-1 py-0.5 rounded cursor-pointer transition-colors",
        isUnfilled 
          ? "bg-gray-100 text-gray-400 hover:bg-gray-200" // placeholder 样式
          : "bg-blue-100 text-blue-800 hover:bg-blue-200", // 已填写样式
        "border",
        isUnfilled 
          ? "border-gray-200 hover:border-gray-300"
          : "border-blue-200 hover:border-blue-300",
        "select-none", // 防止选择参数标签文本
        className
      )}
      title={
        isUnfilled 
          ? `点击填写参数: ${paramName}${description ? ` (${description})` : ''}` 
          : `参数 ${paramName}: ${value}${description ? ` (${description})` : ''}`
      }
    >
      {displayText}
    </span>
  );
});

InlineParameterEditor.displayName = 'InlineParameterEditor';
