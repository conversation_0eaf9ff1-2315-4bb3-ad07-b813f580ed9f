'use client';

import React, { useRef, useEffect, useCallback, useState, useMemo, forwardRef, useImperativeHandle } from 'react';
import { cn } from '@/lib/utils';
import { useParameterizedInput } from './hooks/use-parameterized-input';
import { ParameterRenderer } from './parameter-renderer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import type {
  ParameterizedEditorProps,
  ParameterizedEditorRef,
  ParameterRendererRef
} from './types';

export const ParameterizedEditor = forwardRef<ParameterizedEditorRef, ParameterizedEditorProps>(({
  value,
  onChange,
  onParametersChange,
  onProcessedContentChange,
  placeholder = "输入消息...",
  disabled = false,
  className,
  currentPrompt,
  onParameterClick,
  onKeyDown,
  onFocus,
  onBlur,
  onScroll,
  autoFocus = false,
  onClear
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const parameterContainerRef = useRef<HTMLDivElement>(null);
  const parameterRendererRef = useRef<ParameterRendererRef>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const previousParameterCount = useRef<number>(0);

  // 使用参数化输入 Hook
  const {
    content,
    usedParameters,
    parameterInfos,
    editingParameter,
    parseContent,
    updateContent,
    updateParameter,
    startEditingParameter,
    stopEditingParameter
  } = useParameterizedInput({
    initialValue: value,
    onContentChange: onChange,
    onParametersChange,
    onProcessedContentChange,
    currentPrompt
  });

  // 解析内容为可渲染的部分 - 使用 useMemo 优化
  const parsedParts = useMemo(() => parseContent(content), [parseContent, content]);

  // 清理容器内容的辅助函数
  const cleanupParameterContainer = useCallback(() => {
    if (parameterContainerRef.current) {
      const container = parameterContainerRef.current;
      const childNodes = Array.from(container.childNodes);

      childNodes.forEach(node => {
        // 移除文本节点和非 ParameterRenderer 的元素节点
        if (node.nodeType === Node.TEXT_NODE ||
            (node.nodeType === Node.ELEMENT_NODE &&
             !(node as Element).hasAttribute('data-parameter-renderer'))) {
          try {
            container.removeChild(node);
          } catch (e) {
            // 忽略已经被移除的节点错误
            console.warn('Failed to remove node:', e);
          }
        }
      });
    }
  }, []);

  // 自动调整高度 - 支持两种模式
  const adjustHeight = useCallback(() => {
    const maxHeight = 200; // 最大高度

    // 根据当前模式选择正确的容器
    const activeContainer = usedParameters.length > 0
      ? parameterContainerRef.current
      : editorRef.current;

    if (activeContainer) {
      // 重置高度以获取准确的 scrollHeight
      activeContainer.style.height = 'auto';
      const scrollHeight = activeContainer.scrollHeight;

      // 设置高度
      const newHeight = Math.min(scrollHeight, maxHeight);
      activeContainer.style.height = `${newHeight}px`;

      // 智能控制滚动条显示
      if (scrollHeight > maxHeight) {
        activeContainer.style.overflowY = 'auto';
      } else {
        activeContainer.style.overflowY = 'hidden';
      }
    }
  }, [usedParameters.length]);

  // 暴露 ref 方法
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (usedParameters.length > 0) {
        // 参数模式下，聚焦到参数容器
        parameterContainerRef.current?.focus();
      } else {
        editorRef.current?.focus();
      }
    },
    blur: () => {
      if (usedParameters.length > 0) {
        parameterContainerRef.current?.blur();
      } else {
        editorRef.current?.blur();
      }
    },
    contains: (node: Node) => {
      if (usedParameters.length > 0) {
        return parameterContainerRef.current?.contains(node) || false;
      } else {
        return editorRef.current?.contains(node) || false;
      }
    },
    forceUpdateContent: (newContent: string) => {
      // 强制更新内容，优化性能
      setIsTransitioning(true);
      updateContent(newContent, true);

      // 使用单个 requestAnimationFrame 批量处理
      requestAnimationFrame(() => {
        if (editorRef.current) {
          editorRef.current.textContent = newContent;
        }

        // 如果即将切换到参数化模式，预先清理
        if (usedParameters.length > 0) {
          if (editorRef.current) {
            editorRef.current.textContent = '';
          }
          cleanupParameterContainer();
        }

        adjustHeight();
        setIsTransitioning(false);
      });
    }
  }), [usedParameters, updateContent, cleanupParameterContainer, adjustHeight]);

  // 处理内容变化
  const handleInput = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    if (isComposing) return;

    const target = e.currentTarget;
    const newContent = target.textContent || '';

    // 只有当内容真正改变时才更新
    if (newContent !== content) {
      updateContent(newContent);
    }
  }, [isComposing, updateContent, content]);

  // 处理粘贴事件
  const handlePaste = useCallback((e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');

    // 插入纯文本
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(document.createTextNode(text));
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);

      // 触发内容更新
      const target = e.currentTarget;
      const newContent = target.textContent || '';
      updateContent(newContent);
    }
  }, [updateContent]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLDivElement>) => {
    // 如果有参数正在编辑，不处理键盘事件，让参数编辑器自己处理
    if (editingParameter) {
      return;
    }

    // 处理 Tab 键在参数间跳转
    if (e.key === 'Tab' && usedParameters.length > 0) {
      e.preventDefault();
      // 这里可以实现参数间的跳转逻辑
      // 暂时先调用外部的键盘处理函数
    }

    onKeyDown?.(e);
  }, [usedParameters, onKeyDown, editingParameter]);

  // 处理参数值变化
  const handleParameterValueChange = useCallback((paramName: string, value: string) => {
    updateParameter(paramName, value);
    // 在参数模式下，参数值变化不需要更新 contentEditable 内容
    // 因为内容是通过参数渲染器显示的
  }, [updateParameter]);

  // 处理参数编辑开始
  const handleParameterEditStart = useCallback((paramName: string) => {
    startEditingParameter(paramName);
    onParameterClick?.(paramName);
  }, [startEditingParameter, onParameterClick]);

  // 处理参数编辑结束
  const handleParameterEditEnd = useCallback(() => {
    stopEditingParameter();
  }, [stopEditingParameter]);

  // 处理焦点事件
  const handleFocus = useCallback(() => {
    onFocus?.();
  }, [onFocus]);

  const handleBlur = useCallback(() => {
    onBlur?.();
  }, [onBlur]);

  // 处理输入法事件
  const handleCompositionStart = useCallback(() => {
    setIsComposing(true);
  }, []);

  const handleCompositionEnd = useCallback((e: React.CompositionEvent<HTMLDivElement>) => {
    setIsComposing(false);
    // 输入法结束后手动触发内容更新
    const target = e.currentTarget;
    const newContent = target.textContent || '';
    updateContent(newContent);
  }, [updateContent]);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    onScroll?.();
  }, [onScroll]);

  // 处理清除按钮点击
  const handleClearClick = useCallback(() => {
    // 清空内容
    updateContent('');

    // 通知父组件清除操作（用于重置 currentPrompt）
    onClear?.();

    // 聚焦到编辑器
    setTimeout(() => {
      if (editorRef.current) {
        editorRef.current.focus();
      }
    }, 100);
  }, [updateContent, onClear]);

  // 同步外部 value 变化
  useEffect(() => {
    if (value !== content) {
      updateContent(value, true); // 标记为外部更新，避免循环调用
    }
  }, [value, content, updateContent]);

  // 同步 contentEditable 的内容
  useEffect(() => {
    if (editorRef.current && editorRef.current.textContent !== content) {
      // 保存当前光标位置
      const selection = window.getSelection();
      const range = selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
      const cursorOffset = range ? range.startOffset : 0;

      // 更新内容
      editorRef.current.textContent = content;

      // 尝试恢复光标位置（如果内容长度允许）
      if (selection && content.length > 0) {
        try {
          const newRange = document.createRange();
          const textNode = editorRef.current.firstChild;
          if (textNode && textNode.nodeType === Node.TEXT_NODE) {
            const offset = Math.min(cursorOffset, textNode.textContent?.length || 0);
            newRange.setStart(textNode, offset);
            newRange.setEnd(textNode, offset);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } catch (e) {
          // 如果恢复光标位置失败，忽略错误
          console.warn('Failed to restore cursor position:', e);
        }
      }
    }
  }, [content]);



  // 处理模式切换时的内容清理 - 优化性能
  useEffect(() => {
    const currentParameterCount = usedParameters.length;
    const wasParameterMode = previousParameterCount.current > 0;
    const isParameterMode = currentParameterCount > 0;

    // 只在模式真正切换时才执行操作
    if (wasParameterMode === isParameterMode) {
      return;
    }

    setIsTransitioning(true);

    if (isParameterMode) {
      // 切换到参数化模式
      if (editorRef.current && editorRef.current.textContent !== '') {
        editorRef.current.textContent = '';
      }

      // 使用单个 requestAnimationFrame 批量处理
      requestAnimationFrame(() => {
        cleanupParameterContainer();
        adjustHeight();
        setIsTransitioning(false);

        // 自动聚焦首个参数
        if (parameterRendererRef.current) {
          // 使用多重延迟策略确保聚焦成功
          const focusFirstParam = () => {
            parameterRendererRef.current?.focusFirstParameter();
          };

          // 立即尝试
          focusFirstParam();

          // 使用 setTimeout 再次尝试
          setTimeout(focusFirstParam, 0);

          // 使用更长的延迟作为保险
          setTimeout(focusFirstParam, 50);
        }
      });
    } else {
      // 切换回普通模式
      if (editorRef.current && editorRef.current.textContent !== content) {
        editorRef.current.textContent = content;
      }

      if (parameterContainerRef.current) {
        parameterContainerRef.current.innerHTML = '';
      }

      requestAnimationFrame(() => {
        adjustHeight();
        setIsTransitioning(false);
      });
    }

    // 更新上一次的参数数量
    previousParameterCount.current = currentParameterCount;
  }, [usedParameters.length, content, cleanupParameterContainer, adjustHeight]);

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && editorRef.current) {
      editorRef.current.focus();
    }
  }, [autoFocus]);

  // 当内容变化时调整高度 - 使用防抖避免频繁调整
  useEffect(() => {
    // 如果正在过渡中，延迟调整高度
    if (isTransitioning) {
      return;
    }

    // 使用 requestAnimationFrame 确保在渲染完成后调整
    const timeoutId = setTimeout(() => {
      requestAnimationFrame(() => {
        adjustHeight();
      });
    }, 50); // 50ms 防抖

    return () => clearTimeout(timeoutId);
  }, [content, adjustHeight, isTransitioning]);

  // 如果有参数，使用参数渲染模式；否则使用普通 contentEditable
  if (usedParameters.length > 0) {
    return (
      <>
        <div className="relative" data-parameterized-editor>
          {/* 参数渲染模式 - 直接渲染参数组件 */}
          <div
            ref={parameterContainerRef}
            className={cn(
              "w-full min-h-[66px] max-h-[200px] resize-none pr-3 pl-3 border-none",
              "leading-normal focus:outline-none whitespace-pre-wrap break-words",
              "scrollbar-thin overflow-y-auto transition-all duration-150 ease-in-out",
              disabled && "cursor-not-allowed opacity-50",
              isTransitioning && "opacity-95",
              className
            )}
            style={{
              lineHeight: '1.5',
              paddingTop: '12px',
              paddingRight: '36px', // 为清除按钮留出空间
            }}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            tabIndex={0} // 使 div 可以接收焦点和键盘事件
          >
            <div data-parameter-renderer="true">
              <ParameterRenderer
                ref={parameterRendererRef}
                parts={parsedParts}
                parameterInfos={parameterInfos}
                onParameterValueChange={handleParameterValueChange}
                onParameterEditStart={handleParameterEditStart}
                onParameterEditEnd={handleParameterEditEnd}
              />
            </div>
          </div>

          {/* 参数化模式的占位符 */}
          {!content && (
            <div
              className={cn(
                "absolute inset-0 pointer-events-none text-gray-400",
                "whitespace-pre-wrap break-words",
                className
              )}
              style={{
                lineHeight: '1.5',
                paddingTop: '12px',
                paddingLeft: '12px',
                paddingRight: '48px', // 为清除按钮留出更多空间
                zIndex: 1,
              }}
            >
              {placeholder}
            </div>
          )}

          {/* 清除按钮 */}
          {!disabled && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "absolute top-2 right-2 h-6 w-6 rounded-full",
                "hover:bg-gray-100 hover:text-gray-700",
                "text-gray-400 transition-colors duration-200",
                "focus:ring-2 focus:ring-gray-300 focus:ring-offset-1"
              )}
              onClick={handleClearClick}
              title="清空内容"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </>
    );
  }

  // 普通模式 - 使用 contentEditable
  return (
    <div className="relative" data-parameterized-editor>
      <div
        ref={editorRef}
        contentEditable={!disabled}
        suppressContentEditableWarning
        onInput={handleInput}
        onPaste={handlePaste}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onScroll={handleScroll}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        className={cn(
          "w-full min-h-[66px] max-h-[200px] resize-none pr-3 pl-3 border-none",
          "leading-normal focus:outline-none whitespace-pre-wrap break-words",
          "scrollbar-thin transition-all duration-150 ease-in-out",
          disabled && "cursor-not-allowed opacity-50",
          isTransitioning && "opacity-95",
          className
        )}
        style={{
          lineHeight: '1.5',
          paddingTop: '12px',
        }}
        data-placeholder={placeholder}
      />

      {/* 占位符 - 修复中文输入法兼容性问题 */}
      {!content && !isComposing && (
        <div
          className={cn(
            "absolute inset-0 pointer-events-none text-gray-400",
            "whitespace-pre-wrap break-words",
            className
          )}
          style={{
            lineHeight: '1.5',
            paddingTop: '12px',
            paddingLeft: '12px',
            paddingRight: '12px',
            zIndex: 1,
          }}
        >
          {placeholder}
        </div>
      )}
    </div>
  );
});

ParameterizedEditor.displayName = 'ParameterizedEditor';
