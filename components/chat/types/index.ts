import type { PromptWithParameters } from '@/types';

// ============================================================================
// 基础事件处理类型
// ============================================================================

/** 键盘事件处理函数类型 */
export type KeyboardEventHandler = (e: React.KeyboardEvent) => void;

/** 焦点事件处理函数类型 */
export type FocusEventHandler = () => void;

/** 滚动事件处理函数类型 */
export type ScrollEventHandler = () => void;

/** 参数值变化处理函数类型 */
export type ParameterValueChangeHandler = (paramName: string, value: string) => void;

/** 参数编辑状态变化处理函数类型 */
export type ParameterEditHandler = (paramName: string) => void;

/** 参数编辑结束处理函数类型 */
export type ParameterEditEndHandler = () => void;

/** 内容变化处理函数类型 */
export type ContentChangeHandler = (content: string) => void;

/** 参数集合变化处理函数类型 */
export type ParametersChangeHandler = (parameters: Record<string, string>) => void;

/** 清除操作处理函数类型 */
export type ClearHandler = () => void;

// ============================================================================
// 参数化输入相关类型
// ============================================================================

/** 参数信息接口 */
export interface ParameterInfo {
  name: string;
  value: string;
  isUnfilled: boolean;
  description?: string;
  required?: boolean;
}

/** 参数范围接口（用于文本解析） */
export interface ParameterRange {
  start: number;
  end: number;
  paramName: string;
  value: string;
  isUnfilled: boolean;
}

/** 解析后的内容片段接口 */
export interface ParsedContent {
  text: string;
  isParameter: boolean;
  paramName?: string;
  isUnfilled?: boolean;
}

/** 参数化输入 Hook 选项接口 */
export interface UseParameterizedInputOptions {
  initialValue?: string;
  onContentChange?: ContentChangeHandler;
  onParametersChange?: ParametersChangeHandler;
  onProcessedContentChange?: ContentChangeHandler;
  currentPrompt?: PromptWithParameters | null;
}

// ============================================================================
// 组件 Ref 接口
// ============================================================================

/** 内联参数编辑器 Ref 接口 */
export interface InlineParameterEditorRef {
  focus: () => void;
  startEditing: () => void;
}

/** 参数渲染器 Ref 接口 */
export interface ParameterRendererRef {
  focusFirstParameter: () => void;
  focusNextParameter: (currentParamName: string) => void;
  focusPrevParameter: (currentParamName: string) => void;
}

/** 参数化编辑器 Ref 接口 */
export interface ParameterizedEditorRef {
  focus: () => void;
  blur: () => void;
  contains: (node: Node) => boolean;
  forceUpdateContent: (content: string) => void;
}

// ============================================================================
// 组件 Props 接口
// ============================================================================

/** 内联参数编辑器 Props 接口 */
export interface InlineParameterEditorProps {
  paramName: string;
  value: string;
  isUnfilled: boolean;
  onValueChange: ParameterValueChangeHandler;
  onEditStart?: ParameterEditHandler;
  onEditEnd?: ParameterEditEndHandler;
  onTabNavigation?: (paramName: string, direction: 'next' | 'prev') => void;
  className?: string;
  description?: string;
}

/** 参数渲染器 Props 接口 */
export interface ParameterRendererProps {
  parts: ParsedContent[];
  parameterInfos: ParameterInfo[];
  onParameterValueChange?: ParameterValueChangeHandler;
  onParameterEditStart?: ParameterEditHandler;
  onParameterEditEnd?: ParameterEditEndHandler;
  className?: string;
}

/** 参数化编辑器 Props 接口 */
export interface ParameterizedEditorProps {
  value: string;
  onChange: ContentChangeHandler;
  onParametersChange?: ParametersChangeHandler;
  onProcessedContentChange?: ContentChangeHandler;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  currentPrompt?: PromptWithParameters | null;
  onParameterClick?: ParameterEditHandler;
  // 键盘事件处理
  onKeyDown?: KeyboardEventHandler;
  onFocus?: FocusEventHandler;
  onBlur?: FocusEventHandler;
  // 滚动事件处理
  onScroll?: ScrollEventHandler;
  // 自动聚焦
  autoFocus?: boolean;
  // 清除功能回调
  onClear?: ClearHandler;
}

// ============================================================================
// 提示词选择器相关类型
// ============================================================================

/** 提示词选择触发方式 */
export type PromptTriggerMode = 'slash' | 'button';

/** 提示词选择处理函数类型 */
export type PromptSelectHandler = (prompt: PromptWithParameters) => void;

/** 提示词选择器状态接口 */
export interface PromptSelectorState {
  isOpen: boolean;
  prompts: PromptWithParameters[];
  isLoading: boolean;
  selectedIndex: number;
  triggerMode: PromptTriggerMode | null;
  triggerPosition: number | null;
}

/** 提示词选择器 Hook 选项接口 */
export interface UsePromptSelectorOptions {
  onPromptSelect?: PromptSelectHandler;
  onStateChange?: (state: PromptSelectorState) => void;
}

/** 提示词选择器组件 Props 接口 */
export interface PromptSelectorProps {
  isOpen: boolean;
  prompts: PromptWithParameters[];
  isLoading: boolean;
  selectedIndex: number;
  triggerMode: PromptTriggerMode | null;
  triggerPosition: number | null;
  onPromptSelect: PromptSelectHandler;
  onClose: () => void;
  onKeyboardNavigation?: (direction: 'up' | 'down' | 'enter' | 'escape') => void;
  className?: string;
}

/** 提示词列表组件 Props 接口 */
export interface PromptListProps {
  prompts: PromptWithParameters[];
  isLoading: boolean;
  selectedIndex: number;
  onPromptSelect: PromptSelectHandler;
  className?: string;
}

// ============================================================================
// 聊天输入相关类型
// ============================================================================

/** 聊天状态类型 */
export type ChatStatus = 'ready' | 'submitted' | 'streaming' | 'error';

/** 消息发送处理函数类型 */
export type MessageSendHandler = (message: string) => void;

/** 模型变化处理函数类型 */
export type ModelChangeHandler = (modelId: string) => void;

/** 停止流式传输处理函数类型 */
export type StopStreamingHandler = () => void;

/** useChat Hook 兼容的输入变化处理函数类型 */
export type UseChatInputChangeHandler = (e: React.ChangeEvent<HTMLTextAreaElement>) => void;

/** useChat Hook 兼容的表单提交处理函数类型 */
export type UseChatSubmitHandler = (e: React.FormEvent<HTMLFormElement>) => void;

/** useChat Hook 兼容的输入设置函数类型 */
export type UseChatSetInputHandler = (input: string) => void;

/** useChat Hook 兼容的消息追加函数类型 */
export type UseChatAppendHandler = (message: any) => Promise<string | null | undefined>;

/** 聊天输入组件 Props 接口 */
export interface ChatInputProps {
  onSendMessage?: MessageSendHandler;
  disabled?: boolean;
  placeholder?: string;
  isStreaming?: boolean;
  onStopStreaming?: StopStreamingHandler;
  // 支持直接传入 useChat 的属性
  input?: string;
  handleInputChange?: UseChatInputChangeHandler;
  handleSubmit?: UseChatSubmitHandler;
  setInput?: UseChatSetInputHandler;
  append?: UseChatAppendHandler;
  // 状态指示
  status?: ChatStatus;
  isLoading?: boolean;
  onStop?: StopStreamingHandler;
  // 模型选择相关
  selectedModel?: string;
  onModelChange?: ModelChangeHandler;
  // 参数化相关（用于测试和调试）
  onParametersChange?: ParametersChangeHandler;
  onProcessedMessageChange?: ContentChangeHandler;
  // 初始 prompt 支持（用于 URL 参数功能）
  initialPrompt?: PromptWithParameters | null;
}

// ============================================================================
// 便捷的类型重导出（用于外部使用）
// ============================================================================

// 导出主要的组件类型，方便外部直接使用
export type {
  // 组件 Props
  ChatInputProps as ChatInputPropsType,
  ParameterizedEditorProps as ParameterizedEditorPropsType,
  ParameterRendererProps as ParameterRendererPropsType,
  InlineParameterEditorProps as InlineParameterEditorPropsType,
  PromptSelectorProps as PromptSelectorPropsType,
  PromptListProps as PromptListPropsType,

  // 组件 Ref
  ParameterizedEditorRef as ParameterizedEditorRefType,
  ParameterRendererRef as ParameterRendererRefType,
  InlineParameterEditorRef as InlineParameterEditorRefType,

  // 数据类型
  ParameterInfo as ParameterInfoType,
  ParsedContent as ParsedContentType,
  PromptSelectorState as PromptSelectorStateType,

  // Hook 选项类型
  UseParameterizedInputOptions as UseParameterizedInputOptionsType,
  UsePromptSelectorOptions as UsePromptSelectorOptionsType,

  // 事件处理类型
  ParameterValueChangeHandler as ParameterValueChangeHandlerType,
  ContentChangeHandler as ContentChangeHandlerType,
  ParametersChangeHandler as ParametersChangeHandlerType,
  PromptSelectHandler as PromptSelectHandlerType,

  // 枚举类型
  PromptTriggerMode as PromptTriggerModeType
};
