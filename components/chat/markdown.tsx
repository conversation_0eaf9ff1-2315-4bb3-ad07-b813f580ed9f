import React, { useEffect, useState } from 'react';
import Markdown, { Components } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import RemarkMath from "remark-math";
import RemarkBreaks from "remark-breaks";
import RehypeKatex from "rehype-katex";
import rehypeHighlight from 'rehype-highlight';
import CodeBlock from './code-block';
import { ArtifactCard } from './artifact-card';
import { useArtifact } from '@/hooks/use-artifact';
import type { ArtifactData } from '@/types/artifact';
import 'highlight.js/styles/github.css';
import "katex/dist/katex.min.css";
import 'github-markdown-css/github-markdown-light.css';
import crypto from 'crypto';

// Define custom interface for code component props
interface CodeComponentProps {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

const MarkdownRender = (props: {
  content: string,
}
) => {
  const [processedContent, setProcessedContent] = useState(props.content);
  const [svgBlocks, setSvgBlocks] = useState<{ id: string, content: string }[]>([]);
  const [htmlBlocks, setHtmlBlocks] = useState<{ id: string, content: string }[]>([]);
  const { openArtifact } = useArtifact();

  // 处理括号转义
  function escapeBrackets(text: string) {
    const pattern =
      /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
    return text.replace(
      pattern,
      (match, codeBlock, squareBracket, roundBracket) => {
        if (codeBlock) {
          return codeBlock;
        } else if (squareBracket) {
          return `$$${squareBracket}$$`;
        } else if (roundBracket) {
          return `$${roundBracket}$`;
        }
        return match;
      },
    );
  }

  // 生成基于内容的哈希 ID
  const generateContentBasedId = (content: string, prefix: string = 'svg'): string => {
    // 使用 MD5 哈希算法生成基于内容的哈希值
    // MD5 足够快速且碰撞概率低，适合此用例
    return `${prefix}-${crypto.createHash('md5').update(content).digest('hex').substring(0, 10)}`;
  };

  

  return (
    <>
      {/* 渲染 Markdown 内容 */}
      <Markdown
        remarkPlugins={[RemarkMath, remarkGfm, RemarkBreaks]}
        rehypePlugins={[
          RehypeKatex as any,
          [
            rehypeHighlight as any,
            {
              detect: false,
              ignoreMissing: true,
            },
          ],
        ]}
        components={{
          code({ node, inline, className, children, ...props }: CodeComponentProps) {
            const match = /language-(\w+)/.exec(className || "");

            // 检查是否是 SVG 卡片代码块 - 参考优化后的识别逻辑
            // if (!inline && match && match[1].includes('svg') && className) {
            //   // 从类名中提取 ID
            //   const id = className.replace('hljs language-svg-card-', '');
            //   // 查找对应的 SVG 块
            //   const svgBlock = svgBlocks.find(block => block.id === id);
            //   if (svgBlock) {
            //     const artifact: ArtifactData = {
            //       id: svgBlock.id,
            //       type: 'svg',
            //       content: svgBlock.content,
            //       title: 'SVG 图片',
            //     };

            //     return (
            //       <ArtifactCard
            //         artifact={artifact}
            //         onClick={() => openArtifact(artifact)}
            //       />
            //     );
            //   }
            // }

            // // 检查是否是 HTML 卡片代码块 - 参考优化后的识别逻辑
            // if (!inline && match && match[1].includes('html') && className) {
            //   // 从类名中提取 ID
            //   const id = className.replace('hljs language-html-card-', '');
            //   // 查找对应的 HTML 块
            //   const htmlBlock = htmlBlocks.find(block => block.id === id);
            //   if (htmlBlock) {
            //     const artifact: ArtifactData = {
            //       id: htmlBlock.id,
            //       type: 'html',
            //       content: htmlBlock.content,
            //       title: 'HTML 文件',
            //     };

            //     return (
            //       <ArtifactCard
            //         artifact={artifact}
            //         onClick={() => openArtifact(artifact)}
            //       />
            //     );
            //   }
            // }

            return !inline && match ? (
              <CodeBlock language={match[1]}>{children}</CodeBlock>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          a: (aProps: React.AnchorHTMLAttributes<HTMLAnchorElement> & { 'data-footnote-ref'?: string }) => {
            const isFootnote = aProps['data-footnote-ref'] !== undefined;
            if (isFootnote) {
              return <span style={{ backgroundColor: '#d0e1fd', fontFamily: '', fontSize: '9px', marginRight: '3px', borderRadius: '11px', padding: '1px 4px' }} {...aProps}>{aProps.children}</span>;
            }
            const href = aProps.href || "";
            if (/\.(aac|mp3|opus|wav)$/.test(href)) {
              return (
                <figure>
                  <audio controls src={href}></audio>
                </figure>
              );
            }
            if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {
              return (
                <video controls width="99.9%">
                  <source src={href} />
                </video>
              );
            }
            const isInternal = /^\/(?!\/)|^\.\/|^#/.test(href);
            const target = isInternal ? "_self" : aProps.target ?? "_blank";
            return <a {...aProps} target={target} />;
          },
        }}
      >
        {processedContent}
      </Markdown>
    </>
  );
};

export default MarkdownRender;


