'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Plus,
  MessageSquare,
  Trash2,
  Edit3,
  MoreHorizontal,
  Loader2,
  ChevronDown,
  ChevronRight,
  Settings
} from 'lucide-react';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { useChatStore } from '@/lib/stores/chat-store';
import type { ChatResponse } from '@/types/chat';

interface ChatSidebarProps {
  currentChatId?: string;
  onChatSelect?: () => void;
}

export function ChatSidebar({ currentChatId, onChatSelect }: ChatSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedChat, setSelectedChat] = useState<ChatResponse | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 使用 Zustand store 管理聊天数据
  const {
    chats,
    isLoading,
    isDeleting,
    isUpdating,
    fetchChats,
    deleteChat,
    updateChatTitle,
    getAllChats,
  } = useChatStore();

  // 从路径中提取当前聊天ID，优先使用路径信息
  const getActiveChatId = () => {
    // 优先使用 pathname 中的 chatId
    const match = pathname.match(/^\/chat\/([^\/]+)$/);
    const pathChatId = match ? match[1] : undefined;

    // 如果路径中有 chatId，使用路径中的；否则使用传入的 currentChatId
    return pathChatId || currentChatId;
  };

  const activeChatId = getActiveChatId();

  // 获取合并后的聊天列表（包含临时记录）
  const allChats = getAllChats();

  // 初始化时获取聊天列表
  useEffect(() => {
    fetchChats();
  }, [fetchChats]);

  const handleNewChat = () => {
    router.push('/chat');
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleDeleteClick = (chat: ChatResponse) => {
    setSelectedChat(chat);
    setDeleteDialogOpen(true);
  };

  const handleEditClick = (chat: ChatResponse) => {
    setSelectedChat(chat);
    setEditTitle(chat.title);
    setEditDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (!selectedChat) return;

    // 使用 Zustand store 的 deleteChat 方法
    deleteChat(selectedChat.id);

    // 如果删除的是当前对话，跳转到聊天首页
    if (activeChatId === selectedChat.id) {
      router.replace('/chat');
    }

    // 关闭对话框
    setDeleteDialogOpen(false);
    setSelectedChat(null);
  };

  const handleEditConfirm = () => {
    if (!selectedChat || !editTitle.trim()) return;

    // 使用 Zustand store 的 updateChatTitle 方法
    updateChatTitle(selectedChat.id, editTitle.trim());

    // 关闭对话框
    setEditDialogOpen(false);
    setSelectedChat(null);
    setEditTitle('');
  };



  return (
    <>
      <div className="w-80 md:w-80 sm:w-72 bg-gray-100 flex flex-col h-full overflow-hidden mt-px">
        {/* 头部 */}
        <div className="p-4 items-center flex mt-2 flex-shrink-0">
          <Button
            onClick={handleNewChat}
            className="w-full justify-center gap-2"
          >
            <Plus className="w-4 h-4" />
            新建对话
          </Button>
        </div>

        {/* 对话列表 */}
        <div className="flex-1 overflow-y-auto min-h-0 scrollbar-thin">
          {isLoading ? (
            <div className="text-center text-muted-foreground py-8 px-4">
              <Loader2 className="w-8 h-8 mx-auto mb-2 opacity-50 animate-spin" />
              <p className="text-sm">加载中...</p>
            </div>
          ) : !allChats || allChats.length === 0 ? (
            <div className="text-center text-muted-foreground py-8 px-4">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">暂无对话历史</p>
            </div>
          ) : (
            <div className="py-2">
              {/* 分组标题 */}
              <div
                className="flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-200/50 rounded-md mx-4 mb-2"
                onClick={toggleCollapse}
              >
                <div className="flex items-center gap-2">
                  <Image
                    src="/images/chat/message.svg"
                    alt="最近对话"
                    width={18}
                    height={18}
                    className="text-gray-600"
                  />
                  <span className="text-sm font-medium text-gray-700">最近对话</span>
                </div>
                {isCollapsed ? (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </div>

              {/* 聊天列表 - 可折叠 */}
              {!isCollapsed && (
                <div>
                  {allChats.map((chat) => {
                // 确保 ID 比较的准确性，使用 activeChatId 而不是 currentChatId
                const isCurrentChat = activeChatId && chat.id && String(activeChatId) === String(chat.id);
                const isTemporary = 'isTemporary' in chat && chat.isTemporary;

                return (
                  <div
                    key={chat.id}
                    className={cn(
                      "h-8.5 flex items-center flex-column group relative ml-10 mr-4 my-1 rounded-xl",
                      "hover:bg-gray-200/80",
                      // 参考 GitHub 示例的高亮样式
                      isCurrentChat
                        ? "bg-white border-gray-200 hover:bg-white"
                        : "hover:bg-gray-200"
                      // 移除临时聊天记录的特殊样式，保持一致外观
                    )}
                  >
                    <Link
                      href={`/chat/${chat.id}`}
                      className="block pl-3 pr-2 py-1 w-full"
                      onClick={() => onChatSelect?.()}
                      prefetch={true}
                    >
                      <div className="flex items-center w-full justify-between gap-3">
                        <div className="flex flex-1 items-center min-w-0">
                          <span className={cn(
                            "text-[13px]",
                            isCurrentChat
                              ? "text-foreground"
                              : "text-foreground/80 group-hover:text-foreground"
                          )}>
                            {chat.title}
                          </span>
                        </div>

                        {/* 操作菜单 - 临时聊天记录不显示操作菜单 */}
                        {!isTemporary && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={cn(
                                  "h-7 w-7 p-0 text-gray-600",
                                  "opacity-0 group-hover:opacity-100",
                                  isCurrentChat
                                    ? "hover:bg-gray-100"
                                    : "hover:bg-accent"
                                )}
                                onClick={(e) => e.preventDefault()}
                              >
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditClick(chat)}>
                                <Edit3 className="w-4 h-4 mr-2" />
                                重命名
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteClick(chat)}
                                className="text-destructive"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    </Link>
                  </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部设置按钮 */}
        <div className="p-4 border-t border-gray-200 flex-shrink-0">
          <Button
            variant="ghost"
            onClick={() => router.push('/chat/settings')}
            className="w-full justify-start gap-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200"
          >
            <Settings className="w-4 h-4" />
            对话设置
          </Button>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除对话</DialogTitle>
            <DialogDescription>
              您确定要删除对话 {selectedChat?.title} 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? '删除中...' : '删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑标题对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重命名对话</DialogTitle>
            <DialogDescription>
              为对话设置一个新的标题
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              placeholder="输入新标题"
              maxLength={100}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditDialogOpen(false)}
              disabled={isUpdating}
            >
              取消
            </Button>
            <Button
              onClick={handleEditConfirm}
              disabled={isUpdating || !editTitle.trim()}
            >
              {isUpdating ? '保存中...' : '保存'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
