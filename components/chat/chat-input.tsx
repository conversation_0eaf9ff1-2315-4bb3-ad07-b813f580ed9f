'use client';

import { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Send, Square, Loader2, Lightbulb } from 'lucide-react';
import { cn } from '@/lib/utils';
import { modelDetails } from '@/app/provider/models';
import type { PromptWithParameters } from '@/types';
import { useChatStore } from '@/lib/stores/chat-store';
import { ParameterizedEditor } from './parameterized-editor';
import { PromptSelector } from './prompt-selector';
import { usePromptSelector } from './hooks/use-prompt-selector';
import type {
  ChatInputProps,
  ParameterizedEditorRef
} from './types';

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "输入消息...",
  isStreaming = false,
  onStopStreaming,
  input: externalInput,
  handleInputChange: externalHandleInputChange,
  handleSubmit: externalHandleSubmit,
  setInput: externalSetInput,
  append: externalAppend,
  status = 'ready',
  isLoading = false,
  onStop,
  selectedModel,
  onModelChange,
  onParametersChange,
  onProcessedMessageChange,
  initialPrompt
}: ChatInputProps) {
  const [internalMessage, setInternalMessage] = useState('');
  const editorRef = useRef<ParameterizedEditorRef>(null);

  // 当前选中的 prompt
  const [currentSelectedPrompt, setCurrentSelectedPrompt] = useState<PromptWithParameters | null>(null);

  // 焦点状态管理
  const [isFocused, setIsFocused] = useState(false);

  // 参数状态管理 - 用于动态提示信息
  const [hasParameters, setHasParameters] = useState(false);

  // 用于记录最近选择的提示词（用于解决异步状态更新问题）
  const lastSelectedPromptRef = useRef<PromptWithParameters | null>(null);

  // 使用新的提示词选择器Hook
  const promptSelector = usePromptSelector({
    onPromptSelect: (prompt: PromptWithParameters) => {
      // 记录最近选择的提示词（用于 onCloseAutoFocus 中的判断）
      lastSelectedPromptRef.current = prompt;

      // 处理提示词选择逻辑
      const newMessage = prompt.content;

      // 更新消息内容
      if (externalSetInput) {
        externalSetInput(newMessage);
      } else if (externalHandleInputChange) {
        const mockEvent = {
          target: { value: newMessage }
        } as React.ChangeEvent<HTMLTextAreaElement>;
        externalHandleInputChange(mockEvent);
      } else {
        setMessage(newMessage);
      }

      // 设置当前选中的 prompt
      setCurrentSelectedPrompt(prompt);

      // 智能聚焦：如果有参数，让 ParameterizedEditor 自动聚焦第一个参数；否则聚焦编辑器
      setTimeout(() => {
        if (prompt.parameters && prompt.parameters.length > 0) {
          // 有参数的情况：不手动聚焦，让 ParameterizedEditor 的模式切换逻辑处理
          // ParameterizedEditor 会在切换到参数模式时自动聚焦第一个参数
        } else {
          // 无参数的情况：直接聚焦编辑器
          editorRef.current?.focus();
        }
      }, 100);
    }
  });

  // 获取可用模型列表 - 使用 useMemo 优化
  const availableModels = useMemo(() => Object.entries(modelDetails), []);

  // 使用 Zustand store 管理模型选择状态
  const { selectedModel: storeSelectedModel, setSelectedModel: setStoreSelectedModel } = useChatStore();

  // 使用外部传入的模型或 Zustand store 中的状态 - 使用 useMemo 优化
  const currentModel = useMemo(() =>
    selectedModel !== undefined ? selectedModel : storeSelectedModel,
    [selectedModel, storeSelectedModel]
  );

  const setCurrentModel = useMemo(() =>
    selectedModel !== undefined
      ? (modelId: string) => onModelChange?.(modelId)
      : setStoreSelectedModel,
    [selectedModel, onModelChange, setStoreSelectedModel]
  );

  // 使用外部传入的 input 或内部状态
  const message = externalInput !== undefined ? externalInput : internalMessage;
  const setMessage = useMemo(() =>
    externalInput !== undefined ? () => { } : setInternalMessage,
    [externalInput, setInternalMessage]
  );

  // 存储从 ParameterizedEditor 传来的处理后内容
  const [processedMessage, setProcessedMessage] = useState<string>('');

  // fetchPrompts 功能现在由 promptSelector.fetchPrompts 提供









  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || disabled || isStreaming) {
      return;
    }

    // 立即清空输入框内容（在发送之前）
    const handleClearInput = () => {
      if (externalSetInput) {
        externalSetInput('');
      } else if (externalHandleInputChange) {
        const mockEvent = {
          target: { value: '' }
        } as React.ChangeEvent<HTMLTextAreaElement>;
        externalHandleInputChange(mockEvent);
      } else {
        setMessage('');
      }
    };

    // 使用外部传入的方法
    if (externalHandleSubmit) {
      // 检查是否需要参数替换
      const needsReplacement = processedMessage !== message;

      if (needsReplacement && externalAppend) {
        // 使用 append 方法直接发送参数化消息
        handleClearInput();

        try {
          await externalAppend({
            role: 'user',
            content: processedMessage
          });

          // 清理状态
          setCurrentSelectedPrompt(null);
          setProcessedMessage('');
          setHasParameters(false);
          return;
        } catch (error) {
          console.error('参数化消息发送失败:', error);
        }
      }

      // 普通消息发送
      handleClearInput();
      externalHandleSubmit(e as React.FormEvent<HTMLFormElement>);
    } else if (onSendMessage) {
      // 使用内部的 onSendMessage
      handleClearInput();
      onSendMessage((processedMessage || message).trim());
      setCurrentSelectedPrompt(null);
      setProcessedMessage('');
      setHasParameters(false);
    }
  };

  // 处理内容变化（用于 "/" 触发检测等）
  const handleContentChange = useCallback((newValue: string) => {
    // 使用新的 promptSelector Hook 检测斜杠触发
    promptSelector.checkSlashTrigger(newValue);

    // 如果用户手动编辑内容，且当前有选中的 prompt，检查是否需要清除
    if (currentSelectedPrompt && newValue !== currentSelectedPrompt.content) {
      // 检查新内容是否还包含原 prompt 的内容
      if (!newValue.includes(currentSelectedPrompt.content)) {
        setCurrentSelectedPrompt(null);
        // 清除 prompt 选择时，重置参数状态
        setHasParameters(false);
      }
    }
  }, [promptSelector, currentSelectedPrompt]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 如果提示词选择器打开，键盘导航由 PromptSelector 组件处理
    // 这里只处理正常的输入框键盘事件
    if (promptSelector.isOpen) {
      // 对于提示词选择器的键盘事件，让 PromptSelector 组件处理
      // 只有 Enter 键在没有选择器时才处理消息发送
      if (e.key === 'Enter' && !e.shiftKey) {
        // 如果选择器打开，不处理 Enter 发送消息
        return;
      }
      return;
    }

    // 正常的键盘处理（选择器关闭时）
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  const handleStopStreaming = () => {
    if (onStopStreaming) {
      onStopStreaming();
    } else if (onStop) {
      onStop();
    }
  };

  // 处理 textarea 焦点事件
  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  // 监听 currentSelectedPrompt 变化，更新参数状态
  useEffect(() => {
    const hasParams = currentSelectedPrompt?.parameters && currentSelectedPrompt.parameters.length > 0;
    setHasParameters(!!hasParams);
  }, [currentSelectedPrompt]);

  // 处理初始 prompt（来自 URL 参数）
  useEffect(() => {
    if (initialPrompt && !currentSelectedPrompt) {
      // 设置初始 prompt
      setCurrentSelectedPrompt(initialPrompt);

      // 设置初始内容
      const initialContent = initialPrompt.content;
      if (externalSetInput) {
        externalSetInput(initialContent);
      } else if (externalHandleInputChange) {
        const mockEvent = {
          target: { value: initialContent }
        } as React.ChangeEvent<HTMLTextAreaElement>;
        externalHandleInputChange(mockEvent);
      } else {
        setMessage(initialContent);
      }
    }
  }, [initialPrompt, currentSelectedPrompt, externalSetInput, externalHandleInputChange, setMessage]);

  return (
    <div className="space-y-2">
      <form onSubmit={handleSubmit} className="flex gap-2 items-start">
        <div className={cn(
          "flex-1 border rounded-md transition-shadow duration-200",
          isFocused && "shadow-md"
        )}>
          {/* 参数化编辑器 */}
          <div className="relative">
            <ParameterizedEditor
              ref={editorRef}
              value={message}
              currentPrompt={currentSelectedPrompt}
              onChange={useMemo(() => {
                if (externalSetInput) {
                  return (value: string) => {
                    handleContentChange(value);
                    externalSetInput(value);
                  };
                } else if (externalHandleInputChange) {
                  return (value: string) => {
                    handleContentChange(value);
                    const mockEvent = {
                      target: { value }
                    } as React.ChangeEvent<HTMLTextAreaElement>;
                    externalHandleInputChange(mockEvent);
                  };
                } else {
                  return (value: string) => {
                    handleContentChange(value);
                    setMessage(value);
                  };
                }
              }, [externalSetInput, externalHandleInputChange, handleContentChange, setMessage])}
              onParametersChange={onParametersChange}
              onProcessedContentChange={(content) => {
                setProcessedMessage(content);
                onProcessedMessageChange?.(content);
              }}
              placeholder={placeholder}
              disabled={disabled}
              onKeyDown={handleKeyDown}
              onFocus={handleTextareaFocus}
              onBlur={handleTextareaBlur}
              autoFocus={true}
              className="w-full"
            />

            {/* 斜杠触发的 PromptSelector 组件 - 只处理斜杠模式 */}
            <PromptSelector
              isOpen={promptSelector.isOpen && promptSelector.triggerMode === 'slash'}
              prompts={promptSelector.prompts}
              isLoading={promptSelector.isLoading}
              selectedIndex={promptSelector.selectedIndex}
              triggerMode={promptSelector.triggerMode}
              triggerPosition={promptSelector.triggerPosition}
              onPromptSelect={promptSelector.selectPrompt}
              onClose={promptSelector.closeSelector}
              onKeyboardNavigation={promptSelector.handleKeyboardNavigation}
            />
          </div>

          {/* 底部组件容器 - 改为正常布局流 */}
          <div className="flex items-center justify-between p-2">
            {/* 模型选择器和提示词按钮 - 左侧 */}
            <div className="flex items-center gap-1">
              <Select value={currentModel} onValueChange={setCurrentModel}>
                <SelectTrigger size="sm" className=" w-auto min-w-[120px] text-xs border-none bg-gray-50 hover:bg-gray-100">
                  <SelectValue placeholder="选择模型" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map(([modelId, modelInfo]) => (
                    <SelectItem key={modelId} value={modelId}>
                      <div className="flex flex-col text-xs">
                        <span className="font-medium">{modelInfo.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* 提示词按钮 - 使用新的 PromptSelector 组件 */}
              <Popover
                open={promptSelector.isOpen && promptSelector.triggerMode === 'button'}
                onOpenChange={(open) => {
                  if (open) {
                    promptSelector.openSelector('button');
                  } else {
                    promptSelector.closeSelector();
                  }
                }}
              >
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="bg-gray-50 text-xs hover:text-gray-700 hover:bg-gray-100"
                  >
                    <Lightbulb className="w-4 h-4" />
                    Prompt
                  </Button>
                </PopoverTrigger>

                <PopoverContent
                  className="w-[640px] p-0"
                  align="start"
                  side="top"
                  sideOffset={8}
                  onCloseAutoFocus={(e) => {
                    e.preventDefault();
                    // 智能聚焦：使用 ref 中记录的最近选择的提示词信息
                    // 这样可以避免异步状态更新导致的判断错误
                    const recentPrompt = lastSelectedPromptRef.current;
                    if (!recentPrompt || !recentPrompt.parameters || recentPrompt.parameters.length === 0) {
                      // 只有在没有参数的情况下才聚焦编辑器
                      editorRef.current?.focus();
                    }
                    // 如果有参数，不做任何操作，让 ParameterizedEditor 处理聚焦

                    // 清除记录的提示词信息，避免影响下次选择
                    setTimeout(() => {
                      lastSelectedPromptRef.current = null;
                    }, 200);
                  }}
                >
                  {/* 按钮触发的 PromptSelector 组件 - 只处理按钮模式 */}
                  <PromptSelector
                    isOpen={promptSelector.isOpen && promptSelector.triggerMode === 'button'}
                    prompts={promptSelector.prompts}
                    isLoading={promptSelector.isLoading}
                    selectedIndex={promptSelector.selectedIndex}
                    triggerMode={promptSelector.triggerMode}
                    triggerPosition={promptSelector.triggerPosition}
                    onPromptSelect={promptSelector.selectPrompt}
                    onClose={promptSelector.closeSelector}
                    onKeyboardNavigation={promptSelector.handleKeyboardNavigation}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* 发送/停止按钮 - 右侧 */}
            <div className="flex items-center">
              {/* AI回复时显示停止按钮 */}
              {status === 'streaming' || isStreaming ? (
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={handleStopStreaming}
                  className="h-8 w-8 p-0 hover:bg-red-50 hover:border-red-200"
                  title="停止生成"
                >
                  <Square className="w-4 h-4 text-red-600" />
                </Button>
              ) : status === 'submitted' || isLoading ? (
                /* 消息提交后等待AI响应时显示加载状态 */
                <Button
                  type="button"
                  size="sm"
                  disabled
                  className="h-8 w-8 p-0"
                  title="等待响应"
                >
                  <Loader2 className="w-4 h-4 animate-spin" />
                </Button>
              ) : (
                /* 正常状态显示发送按钮 */
                <Button
                  type="submit"
                  size="sm"
                  disabled={!message.trim() || disabled}
                  className="h-8 w-8 p-0"
                  title="发送消息"
                >
                  <Send className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </form>

      {/* 提示文本 - 根据参数状态动态显示 */}
      <div className="text-xs text-muted-foreground text-center">
        {hasParameters ? "使用 Tab 键快捷切换输入参数" : "按 Enter 发送消息，Shift + Enter 换行"}
      </div>
    </div>
  );
}
