'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { MessageBubble } from './message-bubble';
import { PageLoading } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle } from 'lucide-react';
import type { UIMessage as Message } from '@ai-sdk/react';

/**
 * 从 UIMessage 中提取文本内容
 */
function extractContentFromUIMessage(message: Message): string {
  if (!message.parts || message.parts.length === 0) {
    return '';
  }

  return message.parts
    .filter((part: any) => part.type === 'text')
    .map((part: any) => part.text)
    .join(' ');
}

interface MessageListProps {
  messages: Message[];
  isLoading?: boolean;
  streamingContent?: string;
  onRetry?: () => void;
  error?: Error;
  status?: 'ready' | 'submitted' | 'streaming' | 'error';
}

export function MessageList({
  messages,
  isLoading = false,
  streamingContent,
  onRetry,
  error,
  status = 'ready'
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isAutoScrollingRef = useRef<boolean>(false); // 标记是否正在程序自动滚动

  // 智能滚动控制状态
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // 检查是否在底部的阈值（像素）
  const SCROLL_THRESHOLD = 100;

  // 检查是否在页面底部
  const isAtBottom = useCallback(() => {
    if (!containerRef.current) return false;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    return scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD;
  }, []);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (isAutoScrollEnabled && !isUserScrolling) {
      // 标记正在进行程序自动滚动
      isAutoScrollingRef.current = true;
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

      // 滚动完成后清除标记（延迟清除，确保滚动动画完成）
      setTimeout(() => {
        isAutoScrollingRef.current = false;
      }, 500); // 给滚动动画足够的时间
    }
  }, [isAutoScrollEnabled, isUserScrolling]);

  // 处理滚动事件 - 检测用户手动滚动行为
  const handleScroll = useCallback(() => {
    // 如果是程序自动滚动，忽略此次滚动事件
    if (isAutoScrollingRef.current) {
      return;
    }

    // 确认这是用户主动滚动，暂停自动滚动
    setIsAutoScrollEnabled(false);
    setIsUserScrolling(true);

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置定时器，检测用户停止滚动
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);

      // 如果用户停止滚动后仍在底部，且AI正在回复，则恢复自动滚动
      if (isAtBottom() && (status === 'streaming' || status === 'submitted')) {
        setIsAutoScrollEnabled(true);
      }
    }, 1000); // 1秒后认为用户停止滚动
  }, [isAtBottom, status]);

  // 监听消息变化，处理自动滚动逻辑
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];

    // 如果有新的用户消息，强制滚动到底部（覆盖滚动控制状态）
    if (lastMessage && lastMessage.role === 'user') {
      // 强制启用自动滚动并滚动到底部
      setIsAutoScrollEnabled(true);
      setIsUserScrolling(false);

      // 标记正在进行程序自动滚动
      isAutoScrollingRef.current = true;
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

      // 滚动完成后清除标记
      setTimeout(() => {
        isAutoScrollingRef.current = false;
      }, 500);
      return;
    }

    // AI流式回复时的智能滚动
    if (status === 'streaming' || status === 'submitted') {
      scrollToBottom();
    }
  }, [messages, streamingContent, scrollToBottom, status]);

  // 监听状态变化，重置滚动控制
  useEffect(() => {
    // 新对话开始或AI回复完成时，重置自动滚动状态
    if (status === 'ready' || messages.length === 0) {
      setIsAutoScrollEnabled(true);
      setIsUserScrolling(false);
      isAutoScrollingRef.current = false; // 清除自动滚动标记

      // 清除定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    }
  }, [status, messages.length]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  if (isLoading && messages.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <PageLoading text="加载对话中..." />
      </div>
    );
  }

  if (messages.length === 0 && !streamingContent) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="text-lg font-medium mb-2">开始新对话</div>
          <div className="text-sm">发送消息开始与 AI 助手对话</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="h-full overflow-y-auto scroll-smooth"
      onScroll={handleScroll}
    >
      <div className="max-w-4xl mx-auto px-4 md:px-0 py-4">
        {/* 渲染历史消息 */}
        {messages.map((message, index) => (
          <div key={message.id} className="group">
            <MessageBubble
              role={message.role as 'user' | 'assistant'}
              content={extractContentFromUIMessage(message)}
              timestamp={new Date().toISOString()}
              isStreaming={status === 'streaming' && index === messages.length - 1 && message.role === 'assistant'}
              isLoading={status === 'submitted' && index === messages.length - 1 && message.role === 'assistant'}
            />
          </div>
        ))}

        {/* 显示加载状态 - 当提交消息但还没有AI回复时 */}
        {status === 'submitted' && (messages.length === 0 || messages[messages.length - 1]?.role !== 'assistant') && (
          <div className="group">
            <MessageBubble
              role="assistant"
              content=""
              timestamp={new Date().toISOString()}
              isStreaming={false}
              isLoading={true}
            />
          </div>
        )}

        {/* 错误提示和重试按钮 */}
        {error && onRetry && (
          <div className="flex items-center justify-center py-6">
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 max-w-md w-full mx-4">
              {/* 检查是否是限额错误 */}
              {error.message && (
                error.message.includes('已达到今日使用限额') ||
                error.message.includes('已达到今日免费使用限制')
              ) ? (
                // 限额错误的特殊显示
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 text-destructive mb-3">
                    <AlertCircle className="w-5 h-5" />
                    <span className="font-medium text-base">使用限额已达上限</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                    已达到今日使用限额，请明天再试
                  </p>
                  <div className="text-xs text-muted-foreground/80">
                    每日限额将在 00:00 自动重置
                  </div>
                </div>
              ) : (
                // 其他错误的常规显示
                <>
                  <div className="flex items-center gap-2 text-destructive mb-3">
                    <AlertCircle className="w-4 h-4" />
                    <span className="font-medium">发送失败</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    {error.message || '网络连接异常，请检查网络后重试'}
                  </p>
                  <Button
                    onClick={onRetry}
                    size="sm"
                    variant="outline"
                    className="w-full"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重试
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        {/* 滚动锚点 */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
