'use client';

import { useEffect } from 'react';
import { X, Eye, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArtifactPreview } from './artifact-preview';
import { ArtifactCode } from './artifact-code';
import { cn } from '@/lib/utils';
import type { ArtifactSidebarProps } from '@/types/artifact';

export function ArtifactSidebar({
  isOpen,
  artifact,
  activeTab,
  onClose,
  onTabChange,
}: ArtifactSidebarProps) {
  
  // 处理 ESC 键关闭
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !artifact) {
    return null;
  }

  const getTitle = () => {
    if (artifact.title) return artifact.title;
    return artifact.type === 'html' ? 'HTML 文件' : 'SVG 图形';
  };

  return (
    <>
      {/* 遮罩层 */}
      <div
        className="fixed inset-0 bg-black/50 z-40 transition-opacity"
        onClick={onClose}
      />

      {/* 侧边栏 */}
      <div
        className={cn(
          "fixed right-0 top-0 h-full w-full md:w-2/3 lg:w-1/2 xl:w-2/5 z-50",
          "bg-white shadow-2xl transform transition-transform duration-300 ease-in-out",
          "flex flex-col",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 truncate">
              {getTitle()}
            </h2>
            {artifact.description && (
              <span className="text-sm text-gray-500 truncate">
                - {artifact.description}
              </span>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 标签页内容 */}
        <div className="flex-1 overflow-hidden">
          <Tabs 
            value={activeTab} 
            onValueChange={(value) => onTabChange(value as 'preview' | 'code')}
            className="h-full flex flex-col"
          >
            {/* 标签页头部 */}
            <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
              <TabsTrigger value="preview" className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                预览
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-1">
                <Code className="w-3 h-3" />
                代码
              </TabsTrigger>
            </TabsList>

            {/* 标签页内容 */}
            <div className="flex-1 overflow-hidden p-4">
              <TabsContent value="preview" className="h-full m-0">
                <ArtifactPreview artifact={artifact} />
              </TabsContent>
              
              <TabsContent value="code" className="h-full m-0">
                <ArtifactCode artifact={artifact} />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </>
  );
}
