'use client';

import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import Markdown from '@/components/chat/markdown';

interface MessageBubbleProps {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  isStreaming?: boolean;
  isLoading?: boolean;
}

export function MessageBubble({ role, content, isLoading = false }: MessageBubbleProps) {
  const isUser = role === 'user';

  return (
    <div className={cn(
      'flex w-full my-6',
      isUser ? 'flex-row-reverse' : 'flex-row'
    )}>
      <div className={cn(
        'relative rounded-lg px-4 py-2 group markdown-body',
        isUser
          ? 'text-primary-foreground !bg-gray-100 flex-row-reverse'
          : 'bg-muted text-foreground flex-row  w-full'
      )}>
        {isUser ? (
          <Markdown content={content} />
        ) : (isLoading || !content.trim()) ? (
          <div className="flex items-center gap-2 py-1">
            <Loader2 className="w-4 h-4 animate-spin opacity-60" />
            <span className="text-muted-foreground text-sm">正在思考中...</span>
          </div>
        ) : (
          <Markdown content={content} />
        )}
      </div>
    </div>
  );
}
