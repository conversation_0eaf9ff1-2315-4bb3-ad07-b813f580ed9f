'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { PromptList } from './prompt-list';
import type { PromptSelectorProps } from './types';

export const PromptSelector = React.memo(function PromptSelector({
  isOpen,
  prompts,
  isLoading,
  selectedIndex,
  triggerMode,
  triggerPosition,
  onPromptSelect,
  onClose,
  onKeyboardNavigation,
  className
}: PromptSelectorProps) {
  const slashPopoverRef = useRef<HTMLDivElement>(null);
  const buttonPopoverRef = useRef<HTMLDivElement>(null);

  // 缓存编辑器元素查询结果，避免重复 DOM 查询
  const editorElementRef = useRef<Element | null>(null);

  // 缓存编辑器元素查询
  useEffect(() => {
    editorElementRef.current = document.querySelector('[data-parameterized-editor]');
  }, []);

  // 优化的全局键盘事件处理函数，使用 useCallback 避免重复创建
  const handleGlobalKeyDown = useCallback((e: KeyboardEvent) => {
    try {
      // 检查当前焦点是否在相关容器内
      const activeElement = document.activeElement;
      if (!activeElement) return;

      // 类型安全的 DOM 操作
      const isInSlashPopover = slashPopoverRef.current?.contains(activeElement) ?? false;
      const isInButtonPopover = buttonPopoverRef.current?.contains(activeElement) ?? false;

      // 对于斜杠触发模式，还需要检查是否在编辑器内
      // 使用缓存的编辑器元素引用，避免重复查询
      const isInEditor = editorElementRef.current?.contains(activeElement) ?? false;

      const isRelevantFocus = triggerMode === 'button'
        ? isInButtonPopover
        : (isInSlashPopover || isInEditor);

      if (!isRelevantFocus) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          onKeyboardNavigation?.('down');
          break;
        case 'ArrowUp':
          e.preventDefault();
          onKeyboardNavigation?.('up');
          break;
        case 'Enter':
          e.preventDefault();
          onKeyboardNavigation?.('enter');
          break;
        case 'Escape':
          e.preventDefault();
          onKeyboardNavigation?.('escape');
          break;
      }
    } catch (error) {
      console.warn('Keyboard navigation error:', error);
    }
  }, [triggerMode, onKeyboardNavigation]);

  // 全局键盘事件监听
  useEffect(() => {
    if (!isOpen || prompts.length === 0) return;

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [isOpen, prompts.length, handleGlobalKeyDown]);

  // 优化的点击外部关闭处理函数，添加类型安全和错误处理
  const handleClickOutside = useCallback((event: MouseEvent) => {
    try {
      const target = event.target;

      // 类型安全检查：确保 target 是有效的 Node
      if (!target || !(target instanceof Node)) {
        return;
      }

      // 使用缓存的编辑器元素引用，避免重复查询
      const editorElement = editorElementRef.current;

      const isClickInPopover = slashPopoverRef.current?.contains(target) ?? false;
      const isClickInEditor = editorElement?.contains(target) ?? false;

      if (!isClickInPopover && !isClickInEditor) {
        onClose();
      }
    } catch (error) {
      console.warn('Click outside handler error:', error);
    }
  }, [onClose]);

  // 点击外部关闭（仅针对斜杠触发模式）
  useEffect(() => {
    if (!isOpen || triggerMode !== 'slash') return;

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, triggerMode, handleClickOutside]);

  // 斜杠触发的内联浮层
  if (triggerMode === 'slash' && isOpen && triggerPosition !== null) {
    return (
      <div
        ref={slashPopoverRef}
        className={cn(
          "absolute left-3 bottom-full mb-2 w-[640px] bg-white border rounded-md shadow-lg z-50",
          className
        )}
      >
        <div className="max-h-60 overflow-y-auto">
          <PromptList
            prompts={prompts}
            isLoading={isLoading}
            selectedIndex={selectedIndex}
            onPromptSelect={onPromptSelect}
          />
        </div>
        
        {/* 提示文本 */}
        <div className="border-t px-3 py-2 text-xs text-gray-500 bg-gray-50">
          ↑↓ 切换，Enter 确认，Esc 取消
        </div>
      </div>
    );
  }

  // 按钮触发的弹出框模式 - 只返回内容部分，不包含 PopoverContent
  if (triggerMode === 'button' && isOpen) {
    return (
      <div
        ref={buttonPopoverRef}
        className="w-full"
      >
        <div className="max-h-60 overflow-y-auto" tabIndex={-1}>
          <PromptList
            prompts={prompts}
            isLoading={isLoading}
            selectedIndex={selectedIndex}
            onPromptSelect={onPromptSelect}
          />
        </div>

        {/* 提示文本 */}
        <div className="border-t px-3 py-2 text-xs text-gray-500 bg-gray-50">
          输入 &ldquo;/&rdquo; 快速选择提示词，↑ ↓ 切换，Enter 确认
        </div>
      </div>
    );
  }

  return null;
});
