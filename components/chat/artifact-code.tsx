'use client';

import { useState } from 'react';
import { Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import type { ArtifactCodeProps } from '@/types/artifact';

export function ArtifactCode({ artifact }: ArtifactCodeProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content);
      setCopied(true);
      toast.success('代码已复制到剪贴板');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const getLanguageLabel = () => {
    return artifact.type === 'html' ? 'HTML' : 'SVG';
  };

  return (
    <div className="w-full h-full flex flex-col bg-gray-50">
      {/* 代码头部 */}
      <div className="flex items-center justify-between p-3 bg-gray-100 border-b">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">
            {getLanguageLabel()} 代码
          </span>
          {artifact.title && (
            <span className="text-xs text-gray-500">
              - {artifact.title}
            </span>
          )}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleCopy}
          className="flex items-center gap-1"
        >
          {copied ? (
            <>
              <Check className="w-3 h-3" />
              已复制
            </>
          ) : (
            <>
              <Copy className="w-3 h-3" />
              复制代码
            </>
          )}
        </Button>
      </div>

      {/* 代码内容 */}
      <div className="flex-1 overflow-auto">
        <pre className="p-4 text-sm font-mono leading-relaxed text-gray-800 whitespace-pre-wrap break-words">
          <code className={`language-${artifact.type}`}>
            {artifact.content}
          </code>
        </pre>
      </div>

      {/* 代码统计信息 */}
      <div className="p-2 bg-gray-100 border-t text-xs text-gray-600">
        <div className="flex justify-between">
          <span>
            {artifact.content.split('\n').length} 行
          </span>
          <span>
            {artifact.content.length} 字符
          </span>
        </div>
      </div>
    </div>
  );
}
