'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link'
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Copy, Check, User, Calendar, Tag, Bookmark, BookmarkCheck, MessageCircle } from 'lucide-react';
import type { ExplorePromptWithUser } from '@/lib/explore';
import { toast } from 'sonner';
import { useSession } from '@/hooks/use-session';
import { ButtonLoading } from '@/components/ui/loading';

interface PublicPromptDetailProps {
  prompt: ExplorePromptWithUser;
}

export function PublicPromptDetail({ prompt }: PublicPromptDetailProps) {
  const router = useRouter();
  const { user, isAuthenticated } = useSession();
  const [copiedContent, setCopiedContent] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      setCopiedContent(true);
      toast.success('内容已复制到剪贴板');
      setTimeout(() => setCopiedContent(false), 2000);
    } catch (error) {
      console.error('Failed to copy content:', error);
      toast.error('复制失败');
    }
  };

  // 检查保存状态
  const checkSavedStatus = async () => {
    if (!isAuthenticated || !user) return;

    setIsCheckingStatus(true);
    try {
      const response = await fetch(`/api/explore/prompts/${prompt.id}/saved-status`);
      if (response.ok) {
        const data = await response.json();
        setIsSaved(data.isSaved);
      }
    } catch (error) {
      console.error('Error checking saved status:', error);
    } finally {
      setIsCheckingStatus(false);
    }
  };

  // 保存 prompt
  const handleSavePrompt = async () => {
    if (!isAuthenticated || !user) {
      // 未登录，跳转到登录页面
      const currentPath = `/explore/prompt/${prompt.id}`;
      router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
      return;
    }

    if (isSaved) {
      toast.info('您已经保存过这个 Prompt');
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch(`/api/explore/prompts/${prompt.id}/save`, {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setIsSaved(true);
        toast.success('Prompt 保存成功！');
      } else {
        toast.error(data.error || '保存失败，请重试');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      toast.error('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 页面加载时检查保存状态
  useEffect(() => {
    checkSavedStatus();
  }, [isAuthenticated, user, prompt.id]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className=" mx-auto space-y-6">
          {/* 头部 */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="w-4 h-4" />
                返回
              </Button>
              <h1 className="text-2xl font-bold">{prompt.name}</h1>
            </div>

            {/* 保存按钮 */}
            <div className="flex items-center space-x-2">
              <Link href={`/chat?prompt=${prompt.id}`}>
                <Button
                  variant="outline"
                  className="flex items-center"
                ><MessageCircle />对话</Button>
              </Link>
              {isAuthenticated && user && user.id !== prompt.user.id && (
                <Button
                  onClick={handleSavePrompt}
                  disabled={isSaving || isSaved || isCheckingStatus}
                  variant={isSaved ? "secondary" : "default"}
                  className="flex items-center"
                >
                  {isSaving ? (
                    <ButtonLoading text="保存中..." size="sm" />
                  ) : (
                    <>
                      {isSaved ? (
                        <BookmarkCheck className="w-4 h-4" />
                      ) : (
                        <Bookmark className="w-4 h-4" />
                      )}
                      <span>{isSaved ? '已保存' : '保存'}</span>
                    </>
                  )}
                </Button>
              )}

              {!isAuthenticated && (
                <Button
                  onClick={handleSavePrompt}
                  variant="default"
                  className="flex items-center space-x-2"
                >
                  <Bookmark className="w-4 h-4" />
                  <span>保存</span>
                </Button>
              )}
            </div>
          </div>

          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="w-5 h-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {prompt.description && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">描述</h3>
                  <p className="text-gray-700">{prompt.description}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">分类</h3>
                  <Badge variant="secondary">
                    {prompt.category?.name || '未分类'}
                  </Badge>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">作者</h3>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-700">{prompt.user.name}</span>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">创建时间</h3>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-700">
                      {new Date(prompt.createdAt).toLocaleDateString('zh-CN')}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 参数列表 */}
          {prompt.parameters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>参数列表</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {prompt.parameters.map((param) => (
                    <div key={param.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{param.name}</h4>
                        <Badge variant={param.required ? "secondary" : "outline"}>
                          {param.required ? '必需' : '可选'}
                        </Badge>
                      </div>
                      {param.description && (
                        <p className="text-sm text-gray-600">{param.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 内容 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>内容</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyContent}
                  className="flex items-center space-x-2"
                >
                  {copiedContent ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                  <span>{copiedContent ? '已复制' : '复制'}</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-900 font-mono">
                  {prompt.content}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
