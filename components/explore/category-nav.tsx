'use client';

import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface Category {
  id: number;
  name: string;
  promptCount: number;
}

interface CategoryNavProps {
  categories: Category[];
  currentCategoryId?: number;
}

export function CategoryNav({ categories, currentCategoryId }: CategoryNavProps) {
  const router = useRouter();
  const pathname = usePathname();

  const handleCategoryClick = (categoryId?: number) => {
    if (categoryId) {
      router.push(`/explore/category/${categoryId}`);
    } else {
      router.push('/explore');
    }
  };

  const isAllSelected = pathname === '/explore';
  const totalCount = categories.reduce((sum, cat) => sum + cat.promptCount, 0);

  return (
    <div className="bg-white border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-wrap gap-2">
          {/* 全部分类 */}
          <Button
            variant={isAllSelected ? "default" : "ghost"}
            size="sm"
            onClick={() => handleCategoryClick()}
            className={cn(
              "flex items-center gap-2",
              isAllSelected && "bg-blue-600 hover:bg-blue-700"
            )}
          >
            全部
            <Badge 
              variant={isAllSelected ? "secondary" : "outline"} 
              className={cn(
                "text-xs",
                isAllSelected && "bg-blue-100 text-blue-800 border-blue-200"
              )}
            >
              {totalCount}
            </Badge>
          </Button>

          {/* 各个分类 */}
          {(() => {
            // 对分类进行排序，确保"未分类"始终显示在末尾
            const sortedCategories = [...categories].sort((a, b) => {
              // 如果 a 是"未分类"，则排在后面
              if (a.name === '未分类') return 1;
              // 如果 b 是"未分类"，则排在后面
              if (b.name === '未分类') return -1;
              // 其他分类保持原有排序（按名称排序）
              return 0;
            });

            return sortedCategories.map((category) => {
              const isSelected = currentCategoryId === category.id;
              return (
                <Button
                  key={category.id}
                  variant={isSelected ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleCategoryClick(category.id)}
                  className={cn(
                    "flex items-center gap-2",
                    isSelected && "bg-blue-600 hover:bg-blue-700"
                  )}
                >
                  {category.name}
                  <Badge
                    variant={isSelected ? "secondary" : "outline"}
                    className={cn(
                      "text-xs",
                      isSelected && "bg-blue-100 text-blue-800 border-blue-200"
                    )}
                  >
                    {category.promptCount}
                  </Badge>
                </Button>
              );
            });
          })()}
        </div>
      </div>
    </div>
  );
}
