'use client';

import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, Calendar } from 'lucide-react';
import type { ExplorePromptWithUser } from '@/lib/explore';

interface PromptCardProps {
  prompt: ExplorePromptWithUser;
}

export function PromptCard({ prompt }: PromptCardProps) {
  return (
    <Link href={`/explore/prompt/${prompt.id}`}>
      <Card className="h-full hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg font-semibold line-clamp-2 group-hover:text-blue-600 transition-colors">
              {prompt.name}
            </CardTitle>
          </div>
          {prompt.description && (
            <p className="text-sm text-muted-foreground line-clamp-3 mt-2">
              {prompt.description}
            </p>
          )}
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex flex-wrap gap-2 mb-3">
            {prompt.category && (
              <Badge variant="secondary" className="text-xs">
                {prompt.category.name}
              </Badge>
            )}
            {prompt.parameters.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {prompt.parameters.length} 个参数
              </Badge>
            )}
          </div>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span>{prompt.user.name}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{new Date(prompt.createdAt).toLocaleDateString('zh-CN')}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
