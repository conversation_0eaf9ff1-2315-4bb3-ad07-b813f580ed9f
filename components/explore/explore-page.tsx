'use client';

import { useState, useEffect } from 'react';
import { CategoryNav } from './category-nav';
import { PromptGrid } from './prompt-grid';
import { PageLoading } from '@/components/ui/loading';
import type { ExplorePromptWithUser, PaginatedResult } from '@/lib/explore';

interface Category {
  id: number;
  name: string;
  promptCount: number;
}

interface ExplorePageProps {
  initialData: PaginatedResult<ExplorePromptWithUser>;
  categories: Category[];
  currentCategoryId?: number;
}

export function ExplorePage({ initialData, categories, currentCategoryId }: ExplorePageProps) {
  const [data, setData] = useState<PaginatedResult<ExplorePromptWithUser>>(initialData);
  const [loading, setLoading] = useState(false);

  const fetchPrompts = async (page: number = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
      });

      if (currentCategoryId) {
        params.append('categoryId', currentCategoryId.toString());
      }

      const response = await fetch(`/api/explore/prompts?${params}`);
      if (!response.ok) {
        throw new Error('获取 Prompt 列表失败');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    fetchPrompts(page);
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">发现 Prompt</h1>
            <p className="text-gray-600">探索社区分享的优质 Prompt 模板</p>
          </div>
        </div>
      </div>

      {/* 分类导航 */}
      <CategoryNav categories={categories} currentCategoryId={currentCategoryId} />

      {/* 内容区域 */}
      <div className="container mx-auto px-4 py-8">
        {loading ? (
          <PageLoading />
        ) : (
          <PromptGrid data={data} onPageChange={handlePageChange} />
        )}
      </div>
    </div>
  );
}
