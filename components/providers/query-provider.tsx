'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // 数据被认为是新鲜的时间（5分钟）
            staleTime: 1000 * 60 * 5,
            // 缓存时间（10分钟）
            gcTime: 1000 * 60 * 10,
            // 窗口重新获得焦点时重新获取数据
            refetchOnWindowFocus: true,
            // 重新连接时重新获取数据
            refetchOnReconnect: true,
            // 重试次数
            retry: 3,
            // 重试延迟
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            // 重试次数
            retry: 1,
            // 重试延迟
            retryDelay: 1000,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* 开发环境下显示 React Query DevTools */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
